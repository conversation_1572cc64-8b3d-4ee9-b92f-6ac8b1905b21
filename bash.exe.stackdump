Stack trace:
Frame         Function      Args
0007FFFF8E70  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D70) msys-2.0.dll+0x1FE8E
0007FFFF8E70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9148) msys-2.0.dll+0x67F9
0007FFFF8E70  000210046832 (000210286019, 0007FFFF8D28, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E70  000210068E24 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9150  00021006A225 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE00D80000 ntdll.dll
7FFDFF860000 KERNEL32.DLL
7FFDFE680000 KERNELBASE.dll
7FFDFF520000 USER32.dll
7FFDFE050000 win32u.dll
7FFDFEEC0000 GDI32.dll
7FFDFE3F0000 gdi32full.dll
7FFDFEA70000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFDFE200000 ucrtbase.dll
7FFDFF1D0000 advapi32.dll
7FFDFEFA0000 msvcrt.dll
7FFDFEEF0000 sechost.dll
7FFDFF400000 RPCRT4.dll
7FFDFD3E0000 CRYPTBASE.DLL
7FFDFE350000 bcryptPrimitives.dll
7FFE00BA0000 IMM32.DLL
