const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class TicketControlApp {
    constructor() {
        this.currentScreen = 'ready';
        this.systemInfo = null;
        this.isDev = false;
        
        // Admin-System
        this.adminTapCount = 0;
        this.adminTapTimeout = null;
        this.currentPin = '';
        this.adminPin = '1234'; // Standard-PIN (wird aus Config geladen)
        
        // Network monitoring
        this.networkCheckInterval = null;
        this.isOnline = true;
        this.lastNetworkCheck = null;
        this.hasInitialNetworkCheck = false;
        this.networkCheckTimeout = 15000; // 15 Sekunden
        
        this.init();
    }

    async init() {
        console.log('Initialisiere Ticket Control App...');
        
        // Theme-System initialisieren (als erstes!)
        if (window.ThemeManager) {
            this.themeManager = new window.ThemeManager();
            await this.themeManager.initialize();
        }
        
        // Konfiguration laden
        await this.loadConfig();
        
        // Admin-PIN aus Konfiguration laden
        if (this.config && this.config.admin && this.config.admin.pin) {
            this.adminPin = this.config.admin.pin;
        }
        
        // System-Info laden
        this.systemInfo = await ipcRenderer.invoke('get-system-info');
        this.isDev = this.systemInfo.isDev;
        
        // UI initialisieren
        this.setupUI();
        this.setupEventListeners();
        this.setupAdminSystem();
        
        // Network monitoring starten
        this.startNetworkMonitoring();
        
        // Dev-Controls anzeigen falls Dev-Modus
        if (this.isDev) {
            document.getElementById('dev-controls').style.display = 'flex';
        }
        
        // System-Info im Footer anzeigen
        document.getElementById('system-platform').textContent = this.isDev ? 'DEV' : 'PROD';
        
        console.log('App initialisiert', this.systemInfo);
    }

    async loadConfig() {
        try {
            if (typeof ipcRenderer !== 'undefined') {
                // Electron-Umgebung - Config über IPC vom Main-Process laden
                this.config = await ipcRenderer.invoke('get-config');
                console.log('Konfiguration geladen:', this.config.ui);
            } else {
                // Browser-Umgebung (für Testing)
                this.config = {
                    ui: {
                        successDisplayTimeout: 5000,
                        errorDisplayTimeout: 5000,
                        displayTimeout: 3000
                    }
                };
            }
        } catch (error) {
            console.warn('Konfiguration konnte nicht geladen werden:', error);
            // Fallback-Konfiguration
            this.config = {
                ui: {
                    successDisplayTimeout: 5000,
                    errorDisplayTimeout: 5000,
                    displayTimeout: 3000
                }
            };
        }
    }

    setupUI() {
        // Alle Bildschirme initial verbergen
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Bereit-Bildschirm anzeigen
        this.showScreen('ready');
        this.setSystemStatus('offline', 'System wird gestartet...');
    }

    setupEventListeners() {
        // Globaler Keyboard-Listener für Scanner-Eingaben (Keyboard Wedge Mode)
        this.setupScannerKeyboardListener();
        
        // IPC-Nachrichten vom Main-Process
        ipcRenderer.on('system-ready', () => {
            console.log('System bereit empfangen - nur UI-Update, kein Backend-Call');
            // Nur UI zurücksetzen, NICHT das Backend nochmal informieren
            this.showReadyScreen();
        });

        ipcRenderer.on('system-error', (event, error) => {
            console.error('System-Fehler empfangen:', error);
            this.setSystemStatus('offline', 'System-Fehler');
            this.showSystemError(error);
        });

        ipcRenderer.on('ticket-validating', (event, ticketId) => {
            console.log('Ticket-Validierung gestartet:', ticketId);
            this.setSystemStatus('warning', 'Validierung läuft...');
            this.showValidating(ticketId);
        });

        ipcRenderer.on('ticket-valid', (event, data) => {
            console.log('Ticket gültig:', data);
            this.setSystemStatus('online', 'Zugang gewährt');
            this.showSuccess(data);
        });

        ipcRenderer.on('ticket-invalid', (event, data) => {
            console.log('Ticket ungültig:', data);
            this.setSystemStatus('offline', 'Zugang verweigert');
            this.showError(data);
        });

        ipcRenderer.on('validation-error', (event, data) => {
            console.error('Validierungs-Fehler:', data);
            this.setSystemStatus('offline', 'Validierungs-Fehler');
            
            // Prüfe ob es ein Netzwerk-Problem ist UND Offline Backup ist NICHT aktiv
            const isOfflineBackupActive = this.config?.hardware?.gpio?.turnstile?.offlineBackup;
            
            if (data.error && (data.error.includes('ENOTFOUND') || data.error.includes('timeout') || data.error.includes('network') || data.error.includes('fetch'))) {
                if (!isOfflineBackupActive) {
                    // Netzwerk-Problem - zeige Network-Error statt normalen Error (nur wenn Offline Backup nicht aktiv)
                    this.setNetworkStatus(false, 'API-Aufruf fehlgeschlagen', this.config?.api?.baseUrl || 'Unknown');
                    this.showScreen('network-error');
                } else {
                    console.log('Network-Fehler ignoriert - Offline Backup ist aktiv');
                    // Bei aktivem Offline Backup ignorieren wir Netzwerk-Fehler komplett
                }
            } else {
                // Anderer Fehler - zeige normalen Error-Screen
                this.showError({
                    ticketId: data.ticketId,
                    reason: `System-Fehler: ${data.error}`
                });
            }
        });

        ipcRenderer.on('passage-detected', () => {
            console.log('Durchgang erkannt - Person ist durchgegangen');
            // Optional: UI-Feedback für erkannten Durchgang
            this.showNotification('Person durchgegangen', 'success', 2000);
        });

        // Dev-Controls Event Listeners
        if (this.isDev) {
            const manualInput = document.getElementById('manual-ticket-input');
            const scanBtn = document.getElementById('manual-scan-btn');

            scanBtn.addEventListener('click', () => {
                const ticketId = manualInput.value.trim();
                if (ticketId) {
                    console.log('Manueller Scan:', ticketId);
                    
                    // Prüfe Offline Backup Modus
                    if (this.config?.hardware?.gpio?.turnstile?.offlineBackup) {
                        console.log('Offline Backup aktiv - überspringe API-Validierung');
                        this.handleOfflineBackupScan(ticketId);
                        manualInput.value = '';
                        return;
                    }
                    
                    ipcRenderer.invoke('manual-ticket-input', ticketId);
                    manualInput.value = '';
                } else {
                    this.showModal('Fehler', 'Bitte geben Sie eine Ticket-ID ein.');
                }
            });

            manualInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    scanBtn.click();
                }
            });
        }

        // Modal Event Listeners
        document.getElementById('modal-ok').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('modal-cancel').addEventListener('click', () => {
            this.hideModal();
        });

        // Escape-Taste für Modal (wird in setupScannerKeyboardListener integriert)
    }

    setupScannerKeyboardListener() {
        let scanBuffer = '';
        let scanTimeout = null;
        const SCAN_TIMEOUT_MS = 100; // Zeit zwischen Zeichen bei einem Scan
        const MIN_SCAN_LENGTH = 5; // Minimum Länge für gültigen Scan
        
        // Globaler Keydown-Listener für Scanner-Eingaben
        document.addEventListener('keydown', (e) => {
            console.log('Keyboard event:', {
                key: e.key,
                target: e.target.id || e.target.tagName,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                metaKey: e.metaKey
            });
            
            // Escape-Taste für Modal
            if (e.key === 'Escape') {
                this.hideModal();
                return;
            }
            
            // Ignoriere Tastendrücke wenn in Dev-Controls Input-Feld
            if (e.target.id === 'manual-ticket-input') {
                console.log('Ignoring input in manual-ticket-input field');
                return;
            }
            
            // Dev-Keyboard-Shortcuts behandeln
            if (this.isDev && e.ctrlKey) {
                this.handleDevKeyboardShortcuts(e);
                return;
            }
            
            // Behandle Enter-Taste separat (nicht zum Buffer hinzufügen)
            if (e.key === 'Enter') {
                console.log('Enter key detected - processing buffer immediately');
                e.preventDefault();
                
                if (scanBuffer.length >= MIN_SCAN_LENGTH) {
                    // Lösche Timeout da wir sofort verarbeiten
                    if (scanTimeout) {
                        clearTimeout(scanTimeout);
                    }
                    
                    this.processScanBuffer(scanBuffer);
                    scanBuffer = '';
                }
                return;
            }
            
            // Ignoriere Modifier-Tasten und andere Funktionstasten
            if (e.ctrlKey || e.altKey || e.metaKey || e.key.length > 1) {
                console.log('Ignoring modifier/function key:', e.key);
                return;
            }
            
            console.log('Adding to scan buffer:', e.key);
            
            // Verhindere Standard-Verhalten für Scanner-Eingaben
            e.preventDefault();
            
            // Füge Zeichen zum Scan-Buffer hinzu
            scanBuffer += e.key;
            
            // Lösche vorherigen Timeout
            if (scanTimeout) {
                clearTimeout(scanTimeout);
            }
            
            // Setze neuen Timeout für Scan-Ende-Erkennung
            scanTimeout = setTimeout(() => {
                console.log('Processing scan buffer via timeout:', scanBuffer);
                this.processScanBuffer(scanBuffer);
                scanBuffer = '';
            }, SCAN_TIMEOUT_MS);
        });
        
        // Enter-Taste behandeln (Ende des Scans)
        document.addEventListener('keypress', (e) => {
            console.log('Keypress event:', {
                key: e.key,
                target: e.target.id || e.target.tagName,
                scanBufferLength: scanBuffer.length
            });
            
            if (e.target.id === 'manual-ticket-input') {
                return;
            }
            
            if (e.key === 'Enter' && scanBuffer.length >= MIN_SCAN_LENGTH) {
                console.log('Processing scan buffer via Enter key:', scanBuffer);
                e.preventDefault();
                
                // Lösche Timeout da wir sofort verarbeiten
                if (scanTimeout) {
                    clearTimeout(scanTimeout);
                }
                
                this.processScanBuffer(scanBuffer);
                scanBuffer = '';
            }
        });
        
        console.log('Globaler Scanner-Keyboard-Listener eingerichtet');
    }
    
    processScanBuffer(scannedData) {
        console.log('processScanBuffer called with:', scannedData);
        
        // Bereinige Eingabe - entferne alle Whitespace, Zeilenumbrüche und Sonderzeichen
        let cleanedData = scannedData
            .replace(/[\r\n\t\s]/g, '') // Entferne alle Whitespace-Zeichen
            .replace(/[^A-Z0-9]/gi, '') // Entferne alle Nicht-Alphanumerischen Zeichen
            .trim()
            .toUpperCase();
            
        console.log('Cleaned data:', cleanedData);
        
        // Validiere Scan-Daten
        if (cleanedData.length < 5) {
            console.warn('Gescannte Daten zu kurz:', cleanedData);
            return;
        }
        
        // Basis-Validierung für Ticket-Format
        if (!/^[A-Z0-9]+$/.test(cleanedData)) {
            console.warn('Ungültiges Ticket-Format:', cleanedData);
            return;
        }
        
        console.log('Scanner-Keyboard-Eingabe verarbeitet:', cleanedData);
        
        // Prüfe Offline Backup Modus
        if (this.config?.hardware?.gpio?.turnstile?.offlineBackup) {
            console.log('Offline Backup aktiv - überspringe API-Validierung');
            this.handleOfflineBackupScan(cleanedData);
            return;
        }

        // Sende Ticket-ID an Backend
        if (typeof ipcRenderer !== 'undefined') {
            console.log('Sending to backend via IPC:', cleanedData);
            ipcRenderer.invoke('manual-ticket-input', cleanedData);
        } else {
            console.warn('ipcRenderer nicht verfügbar');
        }
    }
    
    handleDevKeyboardShortcuts(e) {
        switch (e.key) {
            case '1':
                e.preventDefault();
                this.showScreen('ready');
                break;
            case '2':
                e.preventDefault();
                this.showValidating('TEST123');
                break;
            case '3':
                e.preventDefault();
                this.showSuccess({
                    ticketId: 'TEST123',
                    validFrom: new Date(),
                    validTo: new Date(Date.now() + 86400000)
                });
                break;
            case '4':
                e.preventDefault();
                this.showError({
                    ticketId: 'TEST123',
                    reason: 'Test-Fehler'
                });
                break;
        }
    }

    setSystemStatus(status, text) {
        // Status-Indikator entfernt - nur noch Konsolen-Logging
        console.log(`System-Status: ${status} - ${text}`);
    }

    showScreen(screenId) {
        // Alle Bildschirme ausblenden
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Gewünschten Bildschirm anzeigen
        const targetScreen = document.getElementById(`${screenId}-screen`);
        if (targetScreen) {
            targetScreen.classList.add('active');
            this.currentScreen = screenId;
            console.log(`Bildschirm gewechselt zu: ${screenId}`);
        } else {
            console.error(`Bildschirm nicht gefunden: ${screenId}`);
        }
    }

    showValidating(ticketId) {
        document.getElementById('validating-ticket-id').textContent = `Ticket: ${ticketId}`;
        this.showScreen('validating');
    }

    showSuccess(data) {
        // Theme auf Erfolgs-Farbe ändern
        if (this.themeManager) {
            const successConfig = {
                ...this.themeManager.originalConfig,
                primaryColor: this.themeManager.originalConfig.successColor || '#3D8D7A'
            };
            this.themeManager.applyTheme(successConfig);
        }
        
        this.showScreen('success');
        
        // Nach konfigurierter Zeit zurück zum Bereit-Bildschirm
        const timeout = this.config?.ui?.successDisplayTimeout || 5000;
        console.log(`Erfolg-Timeout gesetzt auf: ${timeout}ms`);
        setTimeout(() => {
            if (this.currentScreen === 'success') {
                this.resetToReadyState();
            }
        }, timeout);
    }

    showError(data) {
        // Theme auf Fehler-Farbe ändern
        if (this.themeManager) {
            const errorConfig = {
                ...this.themeManager.originalConfig,
                primaryColor: this.themeManager.originalConfig.errorColor || '#D91656'
            };
            this.themeManager.applyTheme(errorConfig);
        }
        
        document.getElementById('error-reason').textContent = data.reason || 'Unbekannter Fehler';
        
        this.showScreen('error');
        
        // Nach konfigurierter Zeit zurück zum Bereit-Bildschirm
        const timeout = this.config?.ui?.errorDisplayTimeout || 5000;
        console.log(`Fehler-Timeout gesetzt auf: ${timeout}ms`);
        setTimeout(() => {
            if (this.currentScreen === 'error') {
                this.resetToReadyState();
            }
        }, timeout);
    }

    showSystemError(error) {
        document.getElementById('system-error-message').textContent = error;
        this.showScreen('system-error');
        
        // Nach 10 Sekunden zurück zum Bereit-Bildschirm versuchen
        setTimeout(() => {
            if (this.currentScreen === 'system-error') {
                this.showScreen('ready');
                this.setSystemStatus('warning', 'System mit Fehlern');
            }
        }, 10000);
    }

    showModal(title, message, showCancel = false) {
        document.getElementById('modal-title').textContent = title;
        document.getElementById('modal-message').textContent = message;
        
        const cancelBtn = document.getElementById('modal-cancel');
        if (showCancel) {
            cancelBtn.style.display = 'inline-block';
        } else {
            cancelBtn.style.display = 'none';
        }
        
        document.getElementById('modal').style.display = 'flex';
    }

    hideModal() {
        document.getElementById('modal').style.display = 'none';
    }

    showReadyScreen() {
        // Nur UI zurücksetzen - KEIN Backend-Call
        if (this.themeManager && this.themeManager.originalConfig) {
            console.log('Setze Theme zurück auf:', this.themeManager.originalConfig.primaryColor);
            this.themeManager.applyTheme(this.themeManager.originalConfig);
        }
        
        this.showScreen('ready');
        this.setSystemStatus('online', 'System bereit');
    }

    async resetToReadyState() {
        // Theme zurück zur ursprünglichen Primärfarbe
        if (this.themeManager && this.themeManager.originalConfig) {
            console.log('Setze Theme zurück auf:', this.themeManager.originalConfig.primaryColor);
            this.themeManager.applyTheme(this.themeManager.originalConfig);
        }
        
        // UI zurück zu Bereit-Zustand
        this.showScreen('ready');
        this.setSystemStatus('online', 'System bereit');
        
        // Backend informieren, dass Frontend bereit ist (NUR beim Timeout-Ende)
        if (typeof ipcRenderer !== 'undefined') {
            try {
                console.log('Frontend signalisiert Backend: Bereit für neuen Scan');
                await ipcRenderer.invoke('frontend-ready');
            } catch (error) {
                console.error('Fehler beim Senden des Frontend-Ready-Signals:', error);
            }
        }
    }

    formatDateTime(dateInput) {
        let date;
        
        if (typeof dateInput === 'string') {
            date = new Date(dateInput);
        } else if (dateInput instanceof Date) {
            date = dateInput;
        } else {
            return 'Ungültiges Datum';
        }
        
        if (isNaN(date.getTime())) {
            return 'Ungültiges Datum';
        }
        
        return date.toLocaleString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }


    // Utility-Methoden für spätere Erweiterungen
    logEvent(event, data = null) {
        console.log(`[${new Date().toISOString()}] ${event}`, data);
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Hier könnte ein Notification-System implementiert werden
        console.log(`Notification (${type}): ${message}`);
    }

    // Admin-System Setup
    setupAdminSystem() {
        // Admin-Trigger Event Listener
        const adminTrigger = document.getElementById('admin-trigger');
        adminTrigger.addEventListener('click', () => {
            this.handleAdminTap();
        });

        // PIN-Keypad Event Listeners
        const pinKeys = document.querySelectorAll('.pin-key');
        pinKeys.forEach(key => {
            key.addEventListener('click', () => {
                const pin = key.getAttribute('data-pin');
                this.handlePinInput(pin);
            });
        });

        // Settings Event Listeners
        this.setupSettingsEventListeners();
    }

    handleAdminTap() {
        this.adminTapCount++;
        console.log(`Admin-Tap: ${this.adminTapCount}/5`);

        // Reset timeout
        if (this.adminTapTimeout) {
            clearTimeout(this.adminTapTimeout);
        }

        // Nach 2 Sekunden Inaktivität zurücksetzen
        this.adminTapTimeout = setTimeout(() => {
            this.adminTapCount = 0;
        }, 2000);

        // Nach 5 Taps PIN-Dialog öffnen
        if (this.adminTapCount >= 5) {
            this.adminTapCount = 0;
            this.showPinDialog();
        }
    }

    showPinDialog() {
        console.log('Zeige PIN-Dialog');
        this.currentPin = '';
        this.updatePinDisplay();
        document.getElementById('pin-error').style.display = 'none';
        document.getElementById('pin-modal').style.display = 'flex';
    }

    hidePinDialog() {
        document.getElementById('pin-modal').style.display = 'none';
        this.currentPin = '';
    }

    handlePinInput(input) {
        if (input === 'cancel') {
            this.hidePinDialog();
            return;
        }

        if (input === 'clear') {
            this.currentPin = '';
            this.updatePinDisplay();
            document.getElementById('pin-error').style.display = 'none';
            return;
        }

        if (this.currentPin.length < 7) {
            this.currentPin += input;
            this.updatePinDisplay();

            // Wenn 7 Stellen erreicht, PIN prüfen
            if (this.currentPin.length === 7) {
                setTimeout(() => {
                    this.checkPin();
                }, 300);
            }
        }
    }

    updatePinDisplay() {
        for (let i = 1; i <= 7; i++) {
            const dot = document.getElementById(`pin-dot-${i}`);
            if (i <= this.currentPin.length) {
                dot.classList.add('filled');
            } else {
                dot.classList.remove('filled');
            }
        }
    }

    checkPin() {
        if (this.currentPin === this.adminPin) {
            console.log('PIN korrekt - öffne Admin-Menu');
            this.hidePinDialog();
            this.showSettingsMenu();
        } else {
            console.log('PIN falsch');
            this.showPinError();
        }
    }

    showPinError() {
        document.getElementById('pin-error').style.display = 'block';
        this.currentPin = '';
        this.updatePinDisplay();

        // Error nach 2 Sekunden ausblenden
        setTimeout(() => {
            document.getElementById('pin-error').style.display = 'none';
        }, 2000);
    }

    showSettingsMenu() {
        // System-Info laden
        this.loadSettingsData();
        document.getElementById('settings-modal').style.display = 'flex';
    }

    hideSettingsMenu() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    loadSettingsData() {
        // System-Informationen anzeigen
        document.getElementById('settings-version').textContent = this.systemInfo.version || '1.0.0';
        document.getElementById('settings-platform').textContent = this.systemInfo.platform || 'Unknown';
        document.getElementById('settings-mode').textContent = this.isDev ? 'Entwicklung' : 'Produktion';

        // Config laden
        if (this.config) {
            // Audio-Einstellungen
            const beepsCheckbox = document.getElementById('setting-beeps');
            const volumeSlider = document.getElementById('setting-volume');
            const volumeValue = document.getElementById('volume-value');
            const durationInput = document.getElementById('setting-duration');

            // Werte setzen (falls verfügbar)
            if (this.config.hardware && this.config.hardware.audio) {
                beepsCheckbox.checked = this.config.hardware.audio.enableSystemBeeps !== false;
                volumeSlider.value = this.config.hardware.audio.volume || 5;
                volumeValue.textContent = volumeSlider.value;
            }

            if (this.config.ui) {
                durationInput.value = this.config.ui.displayTimeout || 3000;
                
                // Display timeout settings
                document.getElementById('setting-display-timeout').value = this.config.ui.displayTimeout || 3000;
                document.getElementById('setting-success-display-timeout').value = this.config.ui.successDisplayTimeout || 5000;
                document.getElementById('setting-error-display-timeout').value = this.config.ui.errorDisplayTimeout || 5000;
            }

            // Turnstile emergency toggle setting
            if (this.config.hardware && this.config.hardware.gpio && this.config.hardware.gpio.turnstile) {
                document.getElementById('setting-emergency-toggle').checked = this.config.hardware.gpio.turnstile.emergencyEnabled === true;
                document.getElementById('setting-offline-backup').checked = this.config.hardware.gpio.turnstile.offlineBackup === true;
            } else {
                // Fallback wenn config nicht existiert
                document.getElementById('setting-emergency-toggle').checked = false;
                document.getElementById('setting-offline-backup').checked = false;
            }

            // Theme color settings
            if (this.config.theme) {
                document.getElementById('setting-primary-color').value = this.config.theme.primaryColor || '#8D8DAA';
                document.getElementById('setting-success-color').value = this.config.theme.successColor || '#16C47F';
                document.getElementById('setting-error-color').value = this.config.theme.errorColor || '#F93827';
            }

            // API-Konfiguration
            if (this.config.api) {
                document.getElementById('setting-api-url').value = this.config.api.baseUrl || '';
                document.getElementById('setting-api-auth').value = this.config.api.authorization || '';
                document.getElementById('setting-api-ruleset').value = this.config.api.ruleSetId || '';
                document.getElementById('setting-api-timeout').value = this.config.api.timeout || 10000;
            }

            // Device-Konfiguration
            if (this.config.device) {
                document.getElementById('setting-device-project').value = this.config.device.projectId || '';
                document.getElementById('setting-device-terminal').value = this.config.device.terminalId || '';
                document.getElementById('setting-device-area').value = this.config.device.areaId || '';
            }
        }
    }

    setupSettingsEventListeners() {
        // Close Button
        document.getElementById('settings-close').addEventListener('click', () => {
            this.hideSettingsMenu();
        });

        // Volume Slider
        const volumeSlider = document.getElementById('setting-volume');
        const volumeValue = document.getElementById('volume-value');
        
        volumeSlider.addEventListener('input', () => {
            volumeValue.textContent = volumeSlider.value;
        });

        // Action Buttons
        document.getElementById('test-audio').addEventListener('click', () => {
            this.testAudio();
        });

        document.getElementById('test-turnstile').addEventListener('click', () => {
            this.testTurnstile();
        });

        document.getElementById('restart-system').addEventListener('click', () => {
            this.confirmRestart();
        });

        document.getElementById('save-config').addEventListener('click', () => {
            this.saveConfiguration();
        });
    }

    testAudio() {
        console.log('Audio-Test angefordert');
        if (typeof ipcRenderer !== 'undefined') {
            ipcRenderer.invoke('admin-test-audio');
        }
    }

    testTurnstile() {
        console.log('Turnstile-Test angefordert');
        if (typeof ipcRenderer !== 'undefined') {
            ipcRenderer.invoke('admin-test-turnstile');
        }
    }

    confirmRestart() {
        this.showModal('System neustarten', 'Möchten Sie das System wirklich neustarten?', true);
        
        // Override modal OK handler für Restart
        const originalOkHandler = document.getElementById('modal-ok').onclick;
        document.getElementById('modal-ok').onclick = () => {
            this.hideModal();
            this.restartSystem();
            // Restore original handler
            document.getElementById('modal-ok').onclick = originalOkHandler;
        };
    }

    restartSystem() {
        console.log('System-Neustart angefordert');
        if (typeof ipcRenderer !== 'undefined') {
            ipcRenderer.invoke('admin-restart-system');
        }
    }

    saveConfiguration() {
        console.log('Speichere Konfiguration');
        
        // Sammle alle Eingabedaten
        const newConfig = {
            api: {
                baseUrl: document.getElementById('setting-api-url').value,
                authorization: document.getElementById('setting-api-auth').value,
                ruleSetId: document.getElementById('setting-api-ruleset').value,
                timeout: parseInt(document.getElementById('setting-api-timeout').value)
            },
            device: {
                projectId: document.getElementById('setting-device-project').value,
                terminalId: document.getElementById('setting-device-terminal').value,
                areaId: document.getElementById('setting-device-area').value
            },
            hardware: {
                audio: {
                    enableSystemBeeps: document.getElementById('setting-beeps').checked,
                    volume: parseInt(document.getElementById('setting-volume').value)
                },
                gpio: {
                    turnstile: {
                        emergencyEnabled: document.getElementById('setting-emergency-toggle').checked,
                        offlineBackup: document.getElementById('setting-offline-backup').checked
                    }
                }
            },
            ui: {
                displayTimeout: parseInt(document.getElementById('setting-display-timeout').value),
                successDisplayTimeout: parseInt(document.getElementById('setting-success-display-timeout').value),
                errorDisplayTimeout: parseInt(document.getElementById('setting-error-display-timeout').value)
            },
            theme: {
                primaryColor: document.getElementById('setting-primary-color').value,
                successColor: document.getElementById('setting-success-color').value,
                errorColor: document.getElementById('setting-error-color').value
            }
        };

        // Validierung
        if (!newConfig.api.baseUrl || !newConfig.api.authorization || !newConfig.api.ruleSetId) {
            this.showModal('Fehler', 'Bitte füllen Sie alle API-Felder aus.');
            return;
        }

        if (!newConfig.device.projectId || !newConfig.device.terminalId || !newConfig.device.areaId) {
            this.showModal('Fehler', 'Bitte füllen Sie alle Device-Felder aus.');
            return;
        }

        // An Backend senden
        if (typeof ipcRenderer !== 'undefined') {
            ipcRenderer.invoke('admin-save-config', newConfig).then((success) => {
                if (success) {
                    this.showModal('Erfolg', 'Konfiguration wurde erfolgreich gespeichert. Starten Sie das System neu, um die Änderungen zu übernehmen.');
                } else {
                    this.showModal('Fehler', 'Konfiguration konnte nicht gespeichert werden.');
                }
            });
        }
    }

    // Keyboard-Shortcuts für Dev-Modus (integriert in setupScannerKeyboardListener)
    setupKeyboardShortcuts() {
        // Diese Funktionalität ist jetzt in handleDevKeyboardShortcuts und 
        // setupScannerKeyboardListener integriert
        console.log('Dev-Keyboard-Shortcuts sind in Scanner-Listener integriert');
    }

    // Network monitoring functions
    startNetworkMonitoring() {
        console.log('Starte Network Monitoring...');
        
        // Initial check
        this.checkNetworkConnectivity();
        
        // Periodic checks
        this.networkCheckInterval = setInterval(() => {
            this.checkNetworkConnectivity();
        }, this.networkCheckTimeout);
    }

    async checkNetworkConnectivity() {
        const now = new Date();
        this.lastNetworkCheck = now;
        
        try {
            // Prüfe API-Verbindung - verwende den echten API-Endpunkt aus der Konfiguration
            const apiUrl = this.config?.api?.baseUrl || 'https://api.bookaroundme.com';
            
            // Erstelle AbortController für Timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 Sekunden Timeout
            
            // Teste den echten API-Endpunkt oder eine einfache Connectivity-Prüfung
            const response = await fetch(`${apiUrl}/checkin/rule_sets`, {
                method: 'HEAD', // HEAD request ist leichter
                signal: controller.signal,
                headers: {
                    'Authorization': this.config?.api?.authorization || ''
                }
            });
            
            clearTimeout(timeoutId);
            
            // Akzeptiere 200, 401, 403 als "online" (Server antwortet)
            if (response.status === 200 || response.status === 401 || response.status === 403) {
                this.setNetworkStatus(true, 'Online', apiUrl);
            } else {
                this.setNetworkStatus(false, `HTTP ${response.status}`, apiUrl);
            }
        } catch (error) {
            console.error('Network check failed:', error);
            
            // Unterscheide zwischen verschiedenen Fehlertypen
            let errorMessage = 'Verbindungsfehler';
            if (error.name === 'AbortError') {
                errorMessage = 'Timeout';
            } else if (error.message.includes('fetch')) {
                errorMessage = 'Keine Internetverbindung';
            }
            
            this.setNetworkStatus(false, errorMessage, this.config?.api?.baseUrl || 'Unknown');
        }
    }

    setNetworkStatus(isOnline, status, server) {
        const wasOnline = this.isOnline;
        this.isOnline = isOnline;
        
        // Update UI elements
        document.getElementById('network-status').textContent = status;
        document.getElementById('network-server').textContent = server;
        document.getElementById('network-last-check').textContent = new Date().toLocaleTimeString();
        
        // Show/hide error screen nur wenn sich der Status ändert UND es nicht der erste Check ist
        // UND Offline Backup ist NICHT aktiv
        const isOfflineBackupActive = this.config?.hardware?.gpio?.turnstile?.offlineBackup;
        
        if (!isOnline && wasOnline && this.hasInitialNetworkCheck && !isOfflineBackupActive) {
            // Went offline - show error screen (aber nicht beim ersten Check und nicht bei aktivem Offline Backup)
            this.showScreen('network-error');
        } else if (isOnline && !wasOnline && this.currentScreen === 'network-error') {
            // Came back online - return to ready screen mit Theme-Reset
            if (this.themeManager && this.themeManager.originalConfig) {
                this.themeManager.applyTheme(this.themeManager.originalConfig);
            }
            this.showScreen('ready');
        }
        
        // Markiere dass der erste Check gemacht wurde
        this.hasInitialNetworkCheck = true;
        
        console.log(`Network status: ${isOnline ? 'Online' : 'Offline'} - ${status}`);
    }

    stopNetworkMonitoring() {
        if (this.networkCheckInterval) {
            clearInterval(this.networkCheckInterval);
            this.networkCheckInterval = null;
        }
    }

    handleOfflineBackupScan(ticketId) {
        console.log(`Offline Backup Scan: ${ticketId}`);
        
        // Zeige Validierungs-Screen kurz an
        this.showValidating(ticketId);
        
        // Nach kurzer Verzögerung automatisch als erfolgreich behandeln
        setTimeout(() => {
            this.showSuccess({
                ticketId: ticketId,
                validFrom: new Date(),
                validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24h gültig
                message: 'Offline-Zugang gewährt'
            });
            
            // Direkt Backend informieren, Turnstile zu öffnen
            if (typeof ipcRenderer !== 'undefined') {
                ipcRenderer.invoke('open-turnstile', { ticketId, offline: true });
            }
        }, 1000); // 1 Sekunde Verzögerung für realistisches Gefühl
    }
}

// App starten wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    window.ticketApp = new TicketControlApp();
});

// Error-Handler für unbehandelte Fehler
window.addEventListener('error', (e) => {
    console.error('Unbehandelter Fehler:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unbehandelte Promise-Rejection:', e.reason);
});