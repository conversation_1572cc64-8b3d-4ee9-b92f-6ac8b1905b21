/* Reset und Basis-Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Custom Properties für dynamische Theming */
:root {
    --primary-color: #667eea;
    --primary-light: #8fa8f3;
    --primary-dark: #4a5bb8;
    --secondary-color: #764ba2;
    --secondary-dark: #5a3878;
    --gradient-secondary: #7f7aea;
    --analogous-1: #6767ea;
    --analogous-2: #67eaea;
    
    --gradient-background: linear-gradient(135deg, #667eea 0%, #7f7aea 100%);
    --gradient-background-alt: linear-gradient(135deg, #8fa8f3 0%, #4a5bb8 100%);
    --gradient-success: linear-gradient(135deg, #4CAF50, #45a049);
    --gradient-error: linear-gradient(135deg, #f44336, #d32f2f);
    --gradient-warning: linear-gradient(135deg, #FF9800, #F57C00);
    --gradient-info: linear-gradient(135deg, #8fa8f3, #667eea);
    --gradient-accent: linear-gradient(135deg, #667eea, #764ba2);
}

html, body {
    width: 840px;
    height: 480px;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-background);
    color: white;
    cursor: none;
}

#app {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 50px 1fr 50px;
    grid-template-areas: 
        "header"
        "main"
        "footer";
}

/* Header */
.header {
    grid-area: header;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
}

.status-indicator {
    /* Status-Indikator entfernt */
}

/* Hauptbereich */
.main-content {
    grid-area: main;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.screen.active {
    opacity: 1;
    transform: scale(1);
    pointer-events: all;
}

/* Bereit-Bildschirm */
.ready-content {
    text-align: center;
    position: relative;
}

/* Scan-Icon entfernt */

.ready-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
    font-weight: 300;
}

.ready-content p {
    font-size: 24px;
    opacity: 0.9;
    margin-bottom: 30px;
}

.pulse-animation {
    width: 120px;
    height: 120px;
    border: 3px solid rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    margin: 0 auto;
    position: relative;
    animation: pulse 2s ease-in-out infinite;
}

.pulse-animation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-inner 2s ease-in-out infinite;
}

/* Pulse Icon für Erfolg/Fehler */
.pulse-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
    font-weight: bold;
    color: white;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

/* Barcode Stripes für Ready-Screen */
.barcode-stripes {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 3px;
    z-index: 10;
}

.barcode-stripes .stripe {
    background: white;
    height: 30px;
    opacity: 0.8;
    animation: stripe-pulse 2s ease-in-out infinite;
}

.barcode-stripes .stripe:nth-child(1) { width: 2px; animation-delay: 0s; }
.barcode-stripes .stripe:nth-child(2) { width: 4px; animation-delay: 0.1s; }
.barcode-stripes .stripe:nth-child(3) { width: 1px; animation-delay: 0.2s; }
.barcode-stripes .stripe:nth-child(4) { width: 3px; animation-delay: 0.3s; }
.barcode-stripes .stripe:nth-child(5) { width: 5px; animation-delay: 0.4s; }
.barcode-stripes .stripe:nth-child(6) { width: 2px; animation-delay: 0.5s; }
.barcode-stripes .stripe:nth-child(7) { width: 1px; animation-delay: 0.6s; }
.barcode-stripes .stripe:nth-child(8) { width: 3px; animation-delay: 0.7s; }

/* Erfolgs-Pulse */
.success-pulse {
    border-color: rgba(255, 255, 255, 0.7);
}

.success-pulse::before {
    background: rgba(255, 255, 255, 0.6);
}

/* Fehler-Pulse */
.error-pulse {
    border-color: rgba(255, 255, 255, 0.7);
}

.error-pulse::before {
    background: rgba(255, 255, 255, 0.6);
}

/* Validierungs-Bildschirm */
.validating-content {
    text-align: center;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-left: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 30px;
}

.validating-content h2 {
    font-size: 34px;
    margin-bottom: 15px;
    font-weight: 300;
}

.validating-content p {
    font-size: 24px;
    opacity: 0.8;
}

/* Erfolg-Bildschirm */
.success-content {
    text-align: center;
    position: relative;
}

.success-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
    font-weight: 300;
}

.success-content p {
    font-size: 24px;
    opacity: 0.9;
    margin-bottom: 30px;
}

/* Fehler-Bildschirm */
.error-content {
    text-align: center;
    position: relative;
}

.error-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
    font-weight: 300;
}

.error-content p {
    font-size: 24px;
    opacity: 0.9;
    margin-bottom: 30px;
}

/* System-Fehler-Bildschirm */
.system-error-content {
    text-align: center;
    max-width: 600px;
}

.system-error-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: #FF9800;
}

.system-error-content h2 {
    font-size: 28px;
    color: #FF9800;
    margin-bottom: 20px;
}

.error-details {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
}

/* Footer */
.footer {
    grid-area: footer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
}

.system-info {
    display: flex;
    gap: 15px;
    opacity: 0.7;
}

/* Dev-Controls */
.dev-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

#manual-ticket-input {
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    width: 150px;
}

#manual-ticket-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.dev-controls button {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s;
}

.dev-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    color: #333;
    padding: 30px;
    border-radius: 12px;
    max-width: 400px;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 15px;
    font-size: 20px;
}

.modal-content p {
    margin-bottom: 25px;
    font-size: 14px;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: #ddd;
    color: #333;
}

.btn-secondary:hover {
    background: #ccc;
}

/* Animationen */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes pulse-inner {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes successPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes glow {
    0% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.4); }
    100% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
}

@keyframes stripe-pulse {
    0%, 100% { opacity: 0.4; transform: scaleY(1); }
    50% { opacity: 0.9; transform: scaleY(1.2); }
}

/* Responsive Anpassungen für kleinere Bildschirme */
@media (max-height: 480px) {
    .scan-icon, .success-icon, .error-icon, .system-error-icon {
        font-size: 60px;
    }
    
    .ready-content h2, .success-content h2, .error-content h2 {
        font-size: 38px;
    }
    
    .access-granted, .access-denied {
        padding: 15px;
    }
}

/* Admin-Trigger (versteckt) */
.admin-trigger {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background: transparent;
    cursor: pointer;
    z-index: 1000;
}

/* PIN Modal Styling - Fullscreen */
.pin-modal {
    background: var(--gradient-background);
}

.pin-modal .modal-content {
    width: 840px;
    height: 480px;
    max-width: 840px;
    max-height: 480px;
    margin: 0;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-background);
    color: white;
    padding: 80px 40px;
    box-sizing: border-box;
}

.pin-layout {
    display: flex;
    align-items: center;
    gap: 80px;
    width: 100%;
    max-width: 720px;
    height: 100%;
}

.pin-left {
    flex: 1;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 250px;
}

.pin-left h3 {
    font-size: 32px;
    margin-bottom: 20px;
    color: white;
    font-weight: 600;
}

.pin-left p {
    font-size: 20px;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

.pin-right {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.pin-display {
    margin: 25px 0;
}

.pin-dots {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 25px;
}

.pin-dot {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transition: all 0.2s ease;
}

.pin-dot.filled {
    background: white;
    border-color: white;
}

.pin-keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    max-width: 280px;
    margin: 0 auto;
}

.pin-key {
    width: 70px;
    height: 70px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pin-key:hover {
    background: white;
    transform: scale(1.05);
}

.pin-key:active {
    transform: scale(0.95);
}

.pin-key.pin-clear,
.pin-key.pin-cancel {
    background: #ff6b6b;
    color: white;
    font-size: 20px;
}

.pin-key.pin-clear:hover,
.pin-key.pin-cancel:hover {
    background: #ff5252;
}

.pin-error {
    color: #ff6b6b;
    font-size: 16px;
    margin-top: 15px;
    animation: shake 0.5s ease-in-out;
    font-weight: 500;
}

/* Settings Modal Styling - Fullscreen */
.settings-modal {
    background: var(--gradient-background);
}

.settings-modal .modal-content {
    width: 840px;
    height: 480px;
    max-width: 840px;
    max-height: 480px;
    margin: 0;
    border-radius: 0;
    overflow-y: auto;
    background: var(--gradient-background);
    color: white;
    padding: 15px;
    box-sizing: border-box;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-header h3 {
    color: white;
    font-size: 20px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.settings-section {
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-section h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item label {
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
}

.info-item span {
    color: white;
    font-weight: bold;
}

.setting-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.setting-row label {
    font-weight: bold;
    color: white;
}

.setting-row input[type="checkbox"] {
    transform: scale(1.2);
}

.setting-row input[type="range"] {
    width: 100px;
    margin-right: 10px;
}

.setting-row input[type="number"] {
    width: 80px;
    padding: 5px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.config-input {
    width: 200px;
    padding: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-family: monospace;
    font-size: 12px;
}

.config-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.config-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

#volume-value {
    min-width: 20px;
    font-weight: bold;
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 100px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: var(--primary-color);
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.action-btn.danger {
    background: #ff6b6b;
}

.action-btn.danger:hover {
    background: #ff5252;
}

.action-btn.success {
    background: #4CAF50;
}

.action-btn.success:hover {
    background: #45a049;
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        font-size: 14px;
    }
}