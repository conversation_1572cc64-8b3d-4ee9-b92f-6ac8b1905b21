{"name": "wizid-kiosk", "version": "1.0.0", "description": "Wizid POS Kiosk Application - Hochformat Display mit Hardware-Integration", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["kiosk", "pos", "electron", "zvt", "tse", "epson", "mqtt"], "author": "Wizid Kiosk Team", "license": "ISC", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"mqtt": "^5.0.0", "axios": "^1.6.0", "winston": "^3.11.0"}, "build": {"appId": "com.wizid.kiosk", "productName": "Wizid Kiosk", "directories": {"output": "dist"}, "files": ["src/**/*", "config/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}