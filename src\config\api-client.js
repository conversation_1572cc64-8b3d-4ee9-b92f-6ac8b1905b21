const https = require('https');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class ApiClient {
  constructor(configPath = './config/local-config.json') {
    this.configPath = path.resolve(configPath);
    this.localConfig = this.loadLocalConfig();
    this.apiConfig = null;
  }

  loadLocalConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      logger.error('Fehler beim Laden der lokalen Konfiguration:', error);
      throw new Error(`Lokale Konfiguration konnte nicht geladen werden: ${error.message}`);
    }
  }

  async loadApiConfig() {
    return new Promise((resolve, reject) => {
      if (!this.localConfig.api.authKey || this.localConfig.api.authKey === 'Bearer YOUR_API_KEY_HERE') {
        reject(new Error('API-Key ist nicht konfiguriert. Bitte local-config.json aktualisieren.'));
        return;
      }

      const options = {
        method: "GET",
        headers: {
          Authorization: this.localConfig.api.authKey,
          'Content-Type': 'application/json',
          'User-Agent': `Wizid-Kiosk/${require('../../package.json').version}`
        },
        timeout: 10000
      };

      logger.info("Starte API-Anfrage an:", this.localConfig.api.configUrl);
      
      const req = https.request(this.localConfig.api.configUrl, options, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          if (res.statusCode === 200) {
            try {
              const jsonData = JSON.parse(data);
              logger.info("API-Konfiguration erfolgreich geladen");

              // Support für beide Formate: snake_case und PascalCase
              if (jsonData.system_config) {
                logger.info("API-Antwort im snake_case-Format erkannt");
                this.apiConfig = this.normalizeConfig(jsonData, 'snake_case');
              } else if (jsonData.SystemConfig) {
                logger.info("API-Antwort im PascalCase-Format erkannt");
                this.apiConfig = this.normalizeConfig(jsonData, 'PascalCase');
              } else {
                const availableKeys = Object.keys(jsonData).join(', ');
                logger.error("Unbekanntes API-Antwortformat. Verfügbare Keys:", availableKeys);
                reject(new Error(`Unbekanntes API-Antwortformat. Keys: ${availableKeys}`));
                return;
              }

              // API-Konfiguration speichern für Offline-Verwendung
              this.saveApiConfigCache(this.apiConfig);
              
              resolve(this.apiConfig);
            } catch (error) {
              logger.error("Fehler beim Parsen der API-Antwort:", error);
              reject(new Error(`JSON-Parse-Fehler: ${error.message}`));
            }
          } else {
            logger.error(`API-Anfrage fehlgeschlagen mit Status: ${res.statusCode}`);
            reject(new Error(`API-Anfrage fehlgeschlagen: HTTP ${res.statusCode}`));
          }
        });
      });

      req.on("error", (error) => {
        logger.error("Netzwerk-Fehler bei der API-Anfrage:", error);
        // Versuche cached config zu laden
        const cachedConfig = this.loadApiConfigCache();
        if (cachedConfig) {
          logger.warn("Verwende gecachte API-Konfiguration aufgrund von Netzwerk-Fehler");
          resolve(cachedConfig);
        } else {
          reject(new Error(`API-Netzwerk-Fehler: ${error.message}`));
        }
      });

      req.on("timeout", () => {
        req.destroy();
        logger.error("API-Anfrage-Timeout");
        reject(new Error("API-Anfrage-Timeout nach 10 Sekunden"));
      });

      req.end();
    });
  }

  normalizeConfig(data, format) {
    // Normalisiere Konfiguration zu einheitlichem snake_case Format
    if (format === 'snake_case') {
      return {
        system_config: data.system_config,
        mqtt_config: data.mqtt_config,
        tse_config: data.tse_config,
        print_config: data.print,
        zvt_config: data.zvt_config
      };
    } else if (format === 'PascalCase') {
      return {
        system_config: data.SystemConfig,
        mqtt_config: data.MqttConfig,
        tse_config: data.TseConfig,
        print_config: data.Print,
        zvt_config: data.ZvtConfig
      };
    }
    return data;
  }

  saveApiConfigCache(config) {
    try {
      const cacheDir = path.join(path.dirname(this.configPath), 'cache');
      if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true });
      }
      
      const cachePath = path.join(cacheDir, 'api-config-cache.json');
      const cacheData = {
        timestamp: new Date().toISOString(),
        config: config
      };
      
      fs.writeFileSync(cachePath, JSON.stringify(cacheData, null, 2));
      logger.info("API-Konfiguration im Cache gespeichert");
    } catch (error) {
      logger.warn("Fehler beim Speichern der API-Konfiguration im Cache:", error);
    }
  }

  loadApiConfigCache() {
    try {
      const cachePath = path.join(path.dirname(this.configPath), 'cache', 'api-config-cache.json');
      if (fs.existsSync(cachePath)) {
        const cacheData = JSON.parse(fs.readFileSync(cachePath, 'utf8'));
        const cacheAge = Date.now() - new Date(cacheData.timestamp).getTime();
        
        // Cache ist 1 Stunde gültig
        if (cacheAge < 3600000) {
          logger.info("Verwende gecachte API-Konfiguration");
          return cacheData.config;
        } else {
          logger.warn("API-Konfiguration-Cache ist veraltet");
        }
      }
    } catch (error) {
      logger.warn("Fehler beim Laden der gecachten API-Konfiguration:", error);
    }
    return null;
  }

  getSystemConfig() {
    return this.apiConfig?.system_config || null;
  }

  getMqttConfig() {
    return this.apiConfig?.mqtt_config || null;
  }

  getTseConfig() {
    return this.apiConfig?.tse_config || null;
  }

  getPrintConfig() {
    return this.apiConfig?.print_config || null;
  }

  getZvtConfig() {
    return this.apiConfig?.zvt_config || null;
  }

  getPosUrl() {
    return this.apiConfig?.system_config?.pos_url || null;
  }

  isConfigLoaded() {
    return this.apiConfig !== null;
  }
}

module.exports = ApiClient;