# Wizid Kiosk Application

Electron-basierte Kiosk-Anwendung für Wizid POS-System im Hochformat mit Hardware-Integration.

## Features

- **Hochformat-Display**: Optimiert für vertikale Touch-Displays
- **Hardware-Integration**: ZVT-Terminal, TSE, Ticket-Drucker
- **API-Konfiguration**: Zentrale Konfiguration über Wizid API
- **MQTT-Integration**: Real-time Kommunikation mit Backend
- **Kiosk-Modus**: Vollbild, Touch-optimiert, sicher

## Installation

### Voraussetzungen

- Node.js 18+ 
- npm oder yarn
- Netzwerk-Zugang zur Wizid API
- Hardware: ZVT-Terminal, Ticket-Drucker (optional)

### Setup

1. **Repository klonen und Dependencies installieren:**
```bash
git clone <repository-url>
cd wizid_kiosk
npm install
```

2. **Lokale Konfiguration erstellen:**
```bash
cp config/local-config.json.example config/local-config.json
```

3. **API-Key in `config/local-config.json` eintragen:**
```json
{
  "api": {
    "configUrl": "https://api.wizid.com/config/kiosk",
    "authKey": "Bearer YOUR_API_KEY_HERE"
  }
}
```

## Verwendung

### Development-Modus
```bash
npm run dev
```

### Produktion
```bash
npm start
```

### Build erstellen
```bash
npm run build
```

## Konfiguration

### Lokale Konfiguration (`config/local-config.json`)

- **api.authKey**: API-Schlüssel für Konfigurationsabruf
- **api.configUrl**: URL für Konfigurationsendpunkt
- **device.kioskId**: Eindeutige Kiosk-ID
- **development.devMode**: Development-Modus (Fenster statt Vollbild)

### API-Konfiguration (automatisch geladen)

Die Anwendung lädt automatisch folgende Konfiguration von der API:

- **system_config**: POS-URL, Tenant, Client-ID
- **mqtt_config**: MQTT-Topics und Broker-Einstellungen  
- **zvt_config**: ZVT-Terminal-Konfiguration
- **tse_config**: TSE-Einstellungen
- **print_config**: Drucker-Konfiguration

## Hardware-Integration

### ZVT-Terminal (EC-Kartenzahlung)
- IP/Port aus API-Konfiguration
- TLV-Protokoll Unterstützung
- Automatische Fehlerbehandlung

### TSE (Technische Sicherheitseinrichtung)
- Epson TSE Integration
- Lokale TSE-Kommunikation
- Compliance mit deutschen Vorschriften

### Ticket-Drucker
- Boca BIDI FGL Unterstützung
- Netzwerk-Drucker über TCP/IP
- ESC/POS Kommandos

## Architektur

```
src/
├── main.js                 # Electron Main Process
├── config/
│   ├── api-client.js      # API-Konfiguration
│   └── config-manager.js  # Konfiguration verwalten  
├── hardware/
│   ├── zvt-terminal.js    # ZVT-Integration
│   ├── tse-manager.js     # TSE-Integration
│   └── printer-manager.js # Drucker-Integration
├── renderer/
│   ├── preload.js         # CSS/JS-Injection
│   └── kiosk-styles.css   # Hochformat-Styles
└── utils/
    ├── logger.js          # Logging-System
    └── error-handler.js   # Fehlerbehandlung
```

## Layout-Transformation

Die Anwendung transformiert das querformat POS-Interface in ein hochformat-optimiertes Layout:

- **Header (10%)**: Kompakte Navigation
- **Hauptbereich (70%)**: Scrollbares Produktgrid (2 Spalten)  
- **Footer (20%)**: Fixer Warenkorb mit EC-Checkout

## Entwicklung

### Debugging
```bash
# Mit DevTools
npm run dev

# Logs anzeigen
tail -f logs/kiosk.log
```

### API-Format Unterstützung

Die Anwendung unterstützt beide API-Formate:
- `snake_case` (Standard)
- `PascalCase` (Legacy)

## Deployment

### Windows
```bash
npm run build
# Erstellt NSIS-Installer in dist/
```

### Linux  
```bash
npm run build
# Erstellt AppImage in dist/
```

## Troubleshooting

### Häufige Probleme

1. **API-Key nicht konfiguriert**
   - Lösung: API-Key in `config/local-config.json` eintragen

2. **Hardware nicht erreichbar**
   - Lösung: IP-Adressen in API-Konfiguration prüfen

3. **Layout nicht korrekt**
   - Lösung: Browser-Cache leeren, CSS-Injection prüfen

### Logs

- **Anwendungs-Logs**: `logs/kiosk.log`
- **Fehler-Logs**: `logs/error.log`
- **Hardware-Logs**: `logs/hardware.log`

## Support

Bei Problemen oder Fragen:

1. Logs prüfen (`logs/` Verzeichnis)
2. Hardware-Status prüfen
3. API-Konfiguration validieren
4. Support kontaktieren mit Log-Dateien