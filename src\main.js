const { app, BrowserWindow, ipcMain, screen, protocol } = require('electron');
const path = require('path');
const logger = require('./utils/logger');
const ApiClient = require('./config/api-client');

class KioskApplication {
  constructor() {
    this.mainWindow = null;
    this.apiClient = new ApiClient();
    this.apiConfig = null;
    this.isReady = false;
  }

  async initialize() {
    try {
      logger.system('Application', 'Initialisierung gestartet');
      
      // API-Konfiguration laden
      await this.loadApiConfiguration();
      
      // Electron-App konfigurieren
      this.setupElectronApp();
      
      logger.system('Application', 'Initialisierung abgeschlossen');
    } catch (error) {
      logger.error('Fehler bei der Initialisierung:', error);
      app.quit();
    }
  }

  async loadApiConfiguration() {
    try {
      logger.system('API', 'Lade Konfiguration');
      this.apiConfig = await this.apiClient.loadApiConfig();
      
      const systemConfig = this.apiClient.getSystemConfig();
      const posUrl = this.apiClient.getPosUrl();
      
      if (!posUrl) {
        throw new Error('Keine pos_url in der API-Konfiguration gefunden');
      }
      
      logger.system('API', 'Konfiguration erfolgreich geladen');
      logger.info(`POS-URL: ${posUrl}`);
      logger.info(`Tenant: ${systemConfig.tenant}`);
      logger.info(`Client-ID: ${systemConfig.client_id}`);
      
    } catch (error) {
      logger.error('Fehler beim Laden der API-Konfiguration:', error);
      throw error;
    }
  }

  setupElectronApp() {
    // App-Events
    app.whenReady().then(() => {
      // Protokoll für lokale Assets registrieren
      protocol.registerFileProtocol('kiosk-asset', (request, callback) => {
        const url = request.url.substr('kiosk-asset://'.length);
        callback({ path: path.join(app.getAppPath(), url) });
      });

      this.createMainWindow();
      this.isReady = true;
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        logger.system('Application', 'Alle Fenster geschlossen - Beende App');
        app.quit();
      }
    });

    app.on('before-quit', () => {
      logger.system('Application', 'Beende Anwendung');
    });

    // IPC-Handler registrieren
    this.setupIpcHandlers();
  }

  createMainWindow() {
    const displays = screen.getAllDisplays();
    const primaryDisplay = displays[0];
    
    logger.system('Display', `Erkannt: ${primaryDisplay.bounds.width}x${primaryDisplay.bounds.height}`);
    
    // Fenster-Konfiguration für Kiosk-Modus
    const windowConfig = {
      width: primaryDisplay.bounds.width,
      height: primaryDisplay.bounds.height,
      fullscreen: true,
      kiosk: true,
      alwaysOnTop: true,
      frame: false,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'renderer', 'preload.js'),
        webSecurity: true
      }
    };

    // Development-Mode anpassen
    const localConfig = this.apiClient.localConfig;
    if (localConfig.development.devMode) {
      // Vollbild und Kioskmodus bleiben immer aktiv
      windowConfig.fullscreen = true;
      windowConfig.kiosk = true;
      windowConfig.webPreferences.devTools = true;
      logger.system('Development', 'Development-Modus aktiviert mit Vollbild und Kioskmodus');
    }

    this.mainWindow = new BrowserWindow(windowConfig);

    // Event-Handler für das Fenster
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      logger.system('Window', 'Hauptfenster angezeigt');
      
      if (localConfig.development.showDevTools) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      logger.system('Window', 'Hauptfenster geschlossen');
    });

    // Navigation-Handler
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const posUrl = this.apiClient.getPosUrl();
      const allowedDomain = new URL(posUrl).origin;
      
      if (!navigationUrl.startsWith(allowedDomain)) {
        event.preventDefault();
        logger.warn(`Navigation blockiert: ${navigationUrl}`);
      }
    });

    // Externe Links blockieren
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      logger.warn(`Externes Fenster blockiert: ${url}`);
      return { action: 'deny' };
    });

    // POS-URL laden
    const posUrl = this.apiClient.getPosUrl();
    logger.system('Window', `Lade POS-URL: ${posUrl}`);
    
    this.mainWindow.loadURL(posUrl).catch(error => {
      logger.error('Fehler beim Laden der POS-URL:', error);
    });
  }

  setupIpcHandlers() {
    // API-Konfiguration an Renderer senden
    ipcMain.handle('get-api-config', () => {
      return {
        system: this.apiClient.getSystemConfig(),
        mqtt: this.apiClient.getMqttConfig(),
        tse: this.apiClient.getTseConfig(),
        print: this.apiClient.getPrintConfig(),
        zvt: this.apiClient.getZvtConfig()
      };
    });

    // ZVT-Payment-Handler
    ipcMain.handle('start-payment', async (event, amount) => {
      try {
        logger.transaction('payment-start', 'ZVT-Zahlung initiiert', amount);
        // TODO: ZVT-Integration implementieren
        return { success: true, transactionId: 'mock-' + Date.now() };
      } catch (error) {
        logger.error('Fehler bei ZVT-Zahlung:', error);
        return { success: false, error: error.message };
      }
    });

    // Drucker-Handler
    ipcMain.handle('print-receipt', async (event, receiptData) => {
      try {
        logger.system('Printer', 'Beleg-Druck initiiert');
        // TODO: Drucker-Integration implementieren
        return { success: true };
      } catch (error) {
        logger.error('Fehler beim Beleg-Druck:', error);
        return { success: false, error: error.message };
      }
    });

    // Ticket-Druck-Handler
    ipcMain.handle('print-ticket', async (event, ticketData) => {
      try {
        logger.system('Printer', 'Ticket-Druck initiiert');
        // TODO: Ticket-Drucker-Integration implementieren
        return { success: true };
      } catch (error) {
        logger.error('Fehler beim Ticket-Druck:', error);
        return { success: false, error: error.message };
      }
    });

    // System-Status
    ipcMain.handle('get-system-status', () => {
      return {
        api: this.apiClient.isConfigLoaded(),
        window: this.mainWindow !== null,
        ready: this.isReady
      };
    });

    // Logging von Renderer
    ipcMain.handle('log-message', (event, level, message, data) => {
      logger[level](`[Renderer] ${message}`, data);
    });
  }

  // Cleanup-Methoden
  async shutdown() {
    logger.system('Application', 'Shutdown initiiert');
    
    try {
      // TODO: Hardware-Verbindungen schließen
      // TODO: MQTT-Verbindung schließen
      
      if (this.mainWindow) {
        this.mainWindow.close();
      }
      
      logger.system('Application', 'Shutdown abgeschlossen');
    } catch (error) {
      logger.error('Fehler beim Shutdown:', error);
    }
  }
}

// Anwendung starten
const kioskApp = new KioskApplication();

// Graceful Shutdown
process.on('SIGTERM', () => {
  logger.system('Process', 'SIGTERM empfangen');
  kioskApp.shutdown().then(() => process.exit(0));
});

process.on('SIGINT', () => {
  logger.system('Process', 'SIGINT empfangen'); 
  kioskApp.shutdown().then(() => process.exit(0));
});

// Unhandled Exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  kioskApp.shutdown().then(() => process.exit(1));
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// App initialisieren
kioskApp.initialize().catch(error => {
  logger.error('Kritischer Fehler bei der Initialisierung:', error);
  process.exit(1);
});