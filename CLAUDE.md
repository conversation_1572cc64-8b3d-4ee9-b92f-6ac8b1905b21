# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Electron-based POS kiosk application for Wizid POS system in portrait format. The application displays an existing online cash register in fullscreen kiosk mode and provides an alternative, portrait-optimized interface through CSS/JavaScript injection.

## Common Development Commands

### Setup and Development
```bash
# Install dependencies
npm install

# Development mode (fullscreen kiosk mode with DevTools)
npm run dev

# Production mode
npm start

# Build application
npm run build

# Create distribution package
npm run dist

# Pack without distribution
npm run pack
```

### Configuration
```bash
# Copy example config
cp config/local-config.json.example config/local-config.json

# View logs
tail -f logs/kiosk.log          # Application logs
tail -f logs/error.log          # Error logs  
tail -f logs/exceptions.log     # Exception logs
tail -f logs/rejections.log     # Promise rejection logs
```

### Testing and Debugging
```bash
# View API configuration cache
cat config/cache/api-config-cache.json

# Clear cache
rm -rf config/cache/*

# Monitor all logs
tail -f logs/*.log
```

## Technische Architektur

**Basis-System:**
- Angular-basierte POS-Anwendung (Wizid) mit TailwindCSS
- Grid-Layout: 6 Spalten (4 für Produktlisting, 2 für Warenkorb)
- Responsive Design mit POS1/POS2/POS3 Breakpoints

**Electron-Integration:**
- Main Process: Fenster-Management, MQTT, ZVT-Terminal und Drucker-Integration
- Renderer Process: Lädt die Online-Kasse-URL
- Preload Script: CSS/JS-Injektion für Layout-Transformation
- API-Client: Konfigurationsdaten und MQTT-Topics abrufen
- Hardware-Interfaces: ZVT-Schnittstelle und Epson EPOS Drucker

**Layout-Transformation (Quer → Hoch):**
- **Hochformat-Display-Layout:** Zweiteilung vertikal
  - **Oberer Bereich (70-80%):** Scrollbarer Produktbereich mit Navigation
  - **Unterer Bereich (20-30%):** Fixer Warenkorb mit Checkout-Button
- Navigation: Kompakte horizontale Tabs oder Carousel
- Produktgrid: 2-3 Spalten für Touch-Optimierung im Hochformat
- Warenkorb: Fix am unteren Bildschirmrand, immer sichtbar
- **Zahlungsoptionen:** Nur EC-Karte (alle anderen ausblenden)

## Key DOM-Selektoren

```javascript
// Haupt-Layout Container
'.grid.grid-cols-6'                    // Hauptlayout-Container
'.col-span-4'                          // Produktbereich (links)
'.col-span-2'                          // Warenkorb-Bereich (rechts)

// Navigation
'app-tab-nav .hide-scrollbar'          // Tab-Navigation
'.grid.grid-cols-10'                   // Navigation-Container

// Produktlisting
'.grid.sm\\:grid-cols-2.md\\:grid-cols-3.POS1\\:grid-cols-4.POS2\\:grid-cols-5'  // Produktgrid
'app-product-listing-item'             // Einzelne Produktkarten

// Warenkorb
'app-shopping-cart'                    // Warenkorb-Komponente
'app-cart-item-wrapper'                // Warenkorb-Items
'app-checkout-button'                  // Checkout-Button

// Checkout-Seite
'app-checkout'                         // Checkout-Container
'app-payment-options'                  // Zahlungsoptionen
'app-numeric-keyboard'                 // Numerische Tastatur
```

## CSS-Injection-Strategie

```css
/* Hochformat-Display-Layout */
.h-screen.overflow-hidden.w-full.flex-col.grid.divide-x {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
}

/* Oberer Bereich: Scrollbarer Produktbereich (70-80%) */
.col-span-4 {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: 75vh !important;
  grid-column: span 1 !important;
}

/* Produktgrid für Hochformat (2-3 Spalten) */
.grid.sm\\:grid-cols-2.md\\:grid-cols-3.POS1\\:grid-cols-4.POS2\\:grid-cols-5 {
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 1rem !important;
}

/* Unterer Bereich: Fixer Warenkorb (20-30%) */
.col-span-2 {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 25vh !important;
  z-index: 1000 !important;
  background: white !important;
  border-top: 2px solid #e5e7eb !important;
}

/* Navigation kompakter machen */
.grid.grid-cols-10 {
  overflow-x: auto !important;
  white-space: nowrap !important;
}

/* Zahlungsoptionen: Nur EC-Karte anzeigen */
app-payment-options .flex.justify-stretch button:not(:has(.fa-credit-card)) {
  display: none !important;
}

/* Touch-optimierte Buttons */
button, .wiz-action-button {
  min-height: 60px !important;
  min-width: 60px !important;
}
```

## Entwicklungsplan

1. ✅ Analyse der DOM-Struktur
2. ✅ Hochformat-Display-Strategie aktualisiert
3. ⏳ Electron-Grundstruktur erstellen
4. ⏳ Preload-Skript für CSS/JS-Injektion
5. ⏳ Hochformat-Layout: Scrollbereich oben, fixer Warenkorb unten
6. ⏳ Zahlungsoptionen auf nur EC-Karte beschränken
7. ⏳ ZVT-Schnittstelle für EC-Terminals implementieren
8. ⏳ Epson EPOS Beleg- und Ticketdruck auf Netzwerk-Bondrucker
9. ⏳ API-Integration für Konfigurationsdaten und MQTT-Topics
10. ⏳ MQTT-Backend-Integration
11. ⏳ Testing und Feinabstimmung

## Layout-Spezifikation Hochformat

**Bildschirmaufteilung:**
- **Header:** Navigation/Tabs (kompakt, ca. 10% der Höhe)
- **Hauptbereich:** Scrollbares Produktgrid (65-70% der Höhe)
- **Footer:** Fixer Warenkorb + Checkout (20-25% der Höhe)

**Produktgrid:**
- 2-3 Spalten für optimale Touch-Bedienung
- Scrollbar für vertikale Navigation durch Produkte
- Größere Touch-Targets (min. 60px)

**Warenkorb:**
- Immer sichtbar am unteren Bildschirmrand
- Kompakte Darstellung der Artikel
- Prominenter EC-Checkout-Button

**Zahlungsflow:**
- Nur EC-Karte als Zahlungsoption
- Alle anderen Zahlungsarten ausblenden
- ZVT-Integration für EC-Terminal-Kommunikation
- Vereinfachter Checkout-Prozess

## Hardware-Integration

**ZVT-Schnittstelle (EC-Terminal):**
- ZVT-Protokoll für EC-Kartenzahlung
- TCP/IP oder serielle Kommunikation
- Status-Monitoring und Transaktions-Handling
- Fehlerbehandlung und Retry-Mechanismen

**Epson EPOS Drucker:**
- Netzwerk-Bondrucker über TCP/IP
- Beleg-Templates für Transaktionen
- Ticket-Templates für Eintrittskarten
- ESC/POS-Kommandos für Layout und Formatierung
- Drucker-Status-Überwachung (Papier, Tinte, Fehler)

**API-Integration:**
- Konfigurationsdaten von zentraler API abrufen
- MQTT-Topics und Broker-Konfiguration
- Terminal-spezifische Einstellungen
- Produkt- und Preisdaten-Synchronisation
- Drucker- und Payment-Terminal-Konfiguration

## Architektur-Komponenten

```javascript
// Main Process Module
├── main.js                 // Electron Main Process
├── config/
│   ├── local-config.json  // Lokale Installation (API-Key)
│   ├── api-client.js      // API-Konfiguration abrufen (mit snake_case/PascalCase Support)
│   └── config-manager.js  // Konfiguration verwalten
├── hardware/
│   ├── zvt-terminal.js    // ZVT-Schnittstelle (IP/Port aus API)
│   ├── tse-manager.js     // TSE (Technische Sicherheitseinrichtung)
│   ├── printer-manager.js // Ticket-Drucker (Boca BIDI)
│   └── hardware-manager.js // Hardware-Koordination
├── communication/
│   ├── mqtt-client.js     // MQTT mit dynamischen Topics aus API
│   └── ipc-handlers.js    // Renderer-Communication
├── renderer/
│   ├── preload.js         // CSS/JS-Injection für pos_url
│   └── kiosk-styles.css   // Hochformat-Styles
└── utils/
    ├── logger.js          // Logging mit API-Config
    └── error-handler.js   // Fehlerbehandlung
```

**API-Client Implementation (Basis):**
```javascript
const https = require('https');
const fs = require('fs');

class ApiClient {
  constructor(configPath = './config/local-config.json') {
    this.localConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  }

  async loadApiConfig() {
    return new Promise((resolve, reject) => {
      const options = {
        method: "GET",
        headers: {
          Authorization: this.localConfig.api.authKey,
        },
      };

      const req = https.request(this.localConfig.api.configUrl, options, (res) => {
        let data = "";
        res.on("data", (chunk) => { data += chunk; });
        res.on("end", () => {
          if (res.statusCode === 200) {
            try {
              const jsonData = JSON.parse(data);
              // Support für beide Formate: snake_case und PascalCase
              if (jsonData.system_config || jsonData.SystemConfig) {
                resolve(jsonData);
              } else {
                reject(new Error("Unbekanntes API-Antwortformat"));
              }
            } catch (error) {
              reject(error);
            }
          } else {
            reject(new Error(`API-Anfrage fehlgeschlagen: ${res.statusCode}`));
          }
        });
      });

      req.on("error", reject);
      req.end();
    });
  }
}
```

## Datenfluss und Integration

**Startup-Sequenz:**
1. Lokale Config laden (API-Key, ConfigUrl)
2. API-Konfiguration abrufen (system_config, mqtt_config, etc.)
3. MQTT-Verbindung etablieren (mit API-Topics)
4. Hardware initialisieren (ZVT-Terminal, TSE, Drucker)
5. Electron-Fenster laden mit pos_url aus API
6. CSS/JS-Injection für Hochformat-Layout

**Zahlungsprozess:**
1. EC-Button → ZVT-Terminal aktivieren
2. Kartenzahlung durchführen
3. Beleg drucken (Epson EPOS)
4. Ticket drucken (falls Eintrittskarte)
5. MQTT-Nachricht an Backend (Transaktionsbestätigung)

**Konfiguration via API (Reales Format):**
```json
{
  "system_config": {
    "tenant": "tenant_TN2KRXSY0CU472CXXEO6K",
    "client": "KDP", 
    "client_id": "cash_register_x4SeZWh1phPH4osnLlcQo",
    "pc_serial": "5CD3258L1Y",
    "pos_url": "https://pos-shop.wizid.com/p/saddasddsa?cash_register_id=cash_register_x4SeZWh1phPH4osnLlcQo&tenant_id=tenant_TN2KRXSY0CU472CXXEO6K"
  },
  "mqtt_config": {
    "topics": {
      "transaction_created": "dt/subject/subject_AKEG8NHQ8PX5262B5H37M/pos-manage/Transaction/created",
      "transaction_updated": "dt/subject/subject_AKEG8NHQ8PX5262B5H37M/pos-manage/Transaction/updated", 
      "payment_created": "dt/subject/subject_AKEG8NHQ8PX5262B5H37M/pos-manage/TransactionPayment/created",
      "payment_updated": "dt/subject/subject_AKEG8NHQ8PX5262B5H37M/pos-manage/TransactionPayment/updated",
      "print_job_finished": "dt/subject/subject_AKEG8NHQ8PX5262B5H37M/pos-manage/PrintJob/finished",
      "tse_data": "cmd/tenant/tenant_TN2KRXSY0CU472CXXEO6K/cash_register_framework/tse/created",
      "tse_finish": "cmd/tenant/tenant_TN2KRXSY0CU472CXXEO6K/cash_register_framework/tse/finished",
      "tse_error": "cmd/tenant/tenant_TN2KRXSY0CU472CXXEO6K/cash_register_framework/tse/error",
      "zvt_data": "cmd/tenant/tenant_TN2KRXSY0CU472CXXEO6K/cash_register_framework/zvt/data"
    }
  },
  "tse_config": {
    "provider": "EPSON",
    "workflow_act": "FRAME", 
    "puk": "123456",
    "admin_pin": "12345",
    "time_admin_pin": "54321",
    "secret_key": "EPSONKEY",
    "client_id": "K001",
    "device_id": "local_TSE",
    "ip": "127.0.0.1",
    "port": "8009",
    "tse_sn": "8C9A5A12C428ADA070F16FC0DABD270037B49D71A729A07044CF2D48A35459B1",
    "tse_public_key": "BIJABNquMI9jbS0tHuIIpyy3yqESvIp6ggUjjM84MM2QhLGM+5UWsxKIv58bzpGUSDL0o0okdJyw/Uv1b5Uty0SoTVNRn9KPyqSEFOyx1CY8fACy0hMgr5cctLu55y+0gA=="
  },
  "print": {
    "ticket_printer_name": "Boca BIDI FGL 26/46 200 DPI"
  },
  "zvt_config": {
    "zvt_ip": "**************",
    "zvt_port": "22000", 
    "is_tlv": true,
    "zvt_password": "000000"
  }
}
```

**Lokale Installations-Config:**
```json
{
  "api": {
    "configUrl": "https://api.wizid.com/config/kiosk", 
    "authKey": "Bearer YOUR_API_KEY_HERE"
  },
  "device": {
    "kioskId": "kiosk-001",
    "location": "Eingang Nord"
  }
}
```

## Node.js Dependencies

```json
{
  "mqtt": "^5.0.0",           // MQTT-Client
  "node-zvt": "^1.0.0",      // ZVT-Terminal Integration
  "epson-epos": "^2.0.0",    // Epson EPOS Drucker
  "axios": "^1.0.0",         // HTTP-Client für API
  "electron": "^28.0.0",     // Electron Framework
  "winston": "^3.0.0"        // Logging
}
```

## Aktuelle Dateien

- `Elements aus Entwicklertools des Browsers in der Artikelübersicht.txt` - DOM-Struktur Produktübersicht
- `Elements aus Entwicklertools des Browsers in der Warenkorbansicht.txt` - DOM-Struktur Checkout  
- `Seitenquelltext in der Artikelübersicht.txt` - HTML-Quellcode Produktübersicht
- `Seitenquelltext in der Warenkorb Ansicht.txt` - HTML-Quellcode Checkout