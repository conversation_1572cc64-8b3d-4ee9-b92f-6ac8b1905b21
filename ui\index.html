<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Kontrollstation</title>
    <link rel="stylesheet" href="style.css">
    <script src="theme.js"></script>
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <!-- Logo entfernt -->
            </div>
            <div class="status-indicator">
                <!-- Status-Dot entfernt -->
            </div>
            <!-- Versteckter Admin-Trigger -->
            <div id="admin-trigger" class="admin-trigger"></div>
        </header>

        <!-- Hauptbereich -->
        <main class="main-content">
            <!-- Bereit-Bildschirm -->
            <div id="ready-screen" class="screen active">
                <div class="ready-content">
                    <h2>Bereit zum Scannen</h2>
                    <p>Bitte halten Sie Ihr Ticket an den Scanner</p>
                    <div class="pulse-animation">
                        <div class="barcode-stripes">
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                            <div class="stripe"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Validierungs-Bildschirm -->
            <div id="validating-screen" class="screen">
                <div class="validating-content">
                    <div class="spinner"></div>
                    <h2>Ticket wird überprüft...</h2>
                    <p id="validating-ticket-id">Ticket: </p>
                </div>
            </div>

            <!-- Erfolg-Bildschirm -->
            <div id="success-screen" class="screen">
                <div class="success-content">
                    <h2>Zugang gewährt</h2>
                    <p>Bitte passieren Sie die Schranke</p>
                    <div class="pulse-animation success-pulse">
                        <div class="pulse-icon">✓</div>
                    </div>
                </div>
            </div>

            <!-- Fehler-Bildschirm -->
            <div id="error-screen" class="screen">
                <div class="error-content">
                    <h2>Zugang verweigert</h2>
                    <p id="error-reason">Bitte wenden Sie sich an das Personal</p>
                    <div class="pulse-animation error-pulse">
                        <div class="pulse-icon">✗</div>
                    </div>
                </div>
            </div>

            <!-- System-Fehler-Bildschirm -->
            <div id="system-error-screen" class="screen">
                <div class="system-error-content">
                    <div class="system-error-icon">⚠️</div>
                    <h2>System-Fehler</h2>
                    <p id="system-error-message"></p>
                    <div class="error-details">
                        <p>Bitte kontaktieren Sie den technischen Support</p>
                    </div>
                </div>
            </div>

            <!-- Netzwerk-Fehler-Bildschirm -->
            <div id="network-error-screen" class="screen">
                <div class="system-error-content">
                    <h2>Netzwerk-Fehler</h2>
                    <div class="error-details">
                        <p>Status: <span id="network-status">Offline</span></p>
                        <p>Server: <span id="network-server">-</span></p>  
                        <p>Letzte Prüfung: <span id="network-last-check">-</span></p>
                        <p style="margin-top: 15px;">Prüfe Verbindung...</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="system-info">
                <span id="system-platform"></span>
            </div>
            <div class="dev-controls" id="dev-controls" style="display: none;">
                <input type="text" id="manual-ticket-input" placeholder="Ticket-ID eingeben...">
                <button id="manual-scan-btn">Scan simulieren</button>
            </div>
        </footer>
    </div>

    <!-- PIN-Eingabe Modal -->
    <div id="pin-modal" class="modal pin-modal" style="display: none;">
        <div class="modal-content pin-modal-content">
            <div class="pin-layout">
                <div class="pin-left">
                    <h3>Admin-Zugang</h3>
                    <p>Bitte geben Sie den PIN-Code ein:</p>
                    <div class="pin-display">
                        <div class="pin-dots">
                            <div class="pin-dot" id="pin-dot-1"></div>
                            <div class="pin-dot" id="pin-dot-2"></div>
                            <div class="pin-dot" id="pin-dot-3"></div>
                            <div class="pin-dot" id="pin-dot-4"></div>
                            <div class="pin-dot" id="pin-dot-5"></div>
                            <div class="pin-dot" id="pin-dot-6"></div>
                            <div class="pin-dot" id="pin-dot-7"></div>
                        </div>
                    </div>
                    <div class="pin-error" id="pin-error" style="display: none;">
                        Falscher PIN-Code
                    </div>
                </div>
                <div class="pin-right">
                    <div class="pin-keypad">
                        <button class="pin-key" data-pin="1">1</button>
                        <button class="pin-key" data-pin="2">2</button>
                        <button class="pin-key" data-pin="3">3</button>
                        <button class="pin-key" data-pin="4">4</button>
                        <button class="pin-key" data-pin="5">5</button>
                        <button class="pin-key" data-pin="6">6</button>
                        <button class="pin-key" data-pin="7">7</button>
                        <button class="pin-key" data-pin="8">8</button>
                        <button class="pin-key" data-pin="9">9</button>
                        <button class="pin-key pin-clear" data-pin="clear">⌫</button>
                        <button class="pin-key" data-pin="0">0</button>
                        <button class="pin-key pin-cancel" data-pin="cancel">✕</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal settings-modal" style="display: none;">
        <div class="modal-content settings-modal-content">
            <div class="settings-header">
                <h3>Administrationsmenü</h3>
                <button id="settings-close" class="close-btn">✕</button>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h4>System-Informationen</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Version:</label>
                            <span id="settings-version">1.0.0</span>
                        </div>
                        <div class="info-item">
                            <label>Plattform:</label>
                            <span id="settings-platform">Linux</span>
                        </div>
                        <div class="info-item">
                            <label>Modus:</label>
                            <span id="settings-mode">Produktion</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>Audio-Einstellungen</h4>
                    <div class="setting-row">
                        <label>System-Beeps aktiviert:</label>
                        <input type="checkbox" id="setting-beeps" checked>
                    </div>
                    <div class="setting-row">
                        <label>I2C Audio-Lautstärke:</label>
                        <input type="range" id="setting-volume" min="0" max="10" value="5">
                        <span id="volume-value">5</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>Theme-Farben</h4>
                    <div class="setting-row">
                        <label>Primärfarbe:</label>
                        <input type="color" id="setting-primary-color" value="#8D8DAA">
                    </div>
                    <div class="setting-row">
                        <label>Erfolgsfarbe:</label>
                        <input type="color" id="setting-success-color" value="#16C47F">
                    </div>
                    <div class="setting-row">
                        <label>Fehlerfarbe:</label>
                        <input type="color" id="setting-error-color" value="#F93827">
                    </div>
                </div>

                <div class="settings-section">
                    <h4>Anzeigedauer</h4>
                    <div class="setting-row">
                        <label>Standard Anzeigedauer (ms):</label>
                        <input type="number" id="setting-display-timeout" min="1000" max="10000" step="500" value="3000">
                    </div>
                    <div class="setting-row">
                        <label>Erfolg Anzeigedauer (ms):</label>
                        <input type="number" id="setting-success-display-timeout" min="1000" max="10000" step="500" value="5000">
                    </div>
                    <div class="setting-row">
                        <label>Fehler Anzeigedauer (ms):</label>
                        <input type="number" id="setting-error-display-timeout" min="1000" max="10000" step="500" value="5000">
                    </div>
                </div>

                <div class="settings-section">
                    <h4>Turnstile-Einstellungen</h4>
                    <div class="setting-row">
                        <label>Öffnungsdauer (ms):</label>
                        <input type="number" id="setting-duration" min="1000" max="10000" step="500" value="3000">
                    </div>
                    <div class="setting-row">
                        <label>Emergency Toggle (manuelle Gegenrichtung):</label>
                        <input type="checkbox" id="setting-emergency-toggle">
                    </div>
                    <div class="setting-row">
                        <label>Offline Backup (alle Karten zulassen):</label>
                        <input type="checkbox" id="setting-offline-backup">
                    </div>
                </div>

                <div class="settings-section">
                    <h4>API-Konfiguration</h4>
                    <div class="setting-row">
                        <label>Base URL:</label>
                        <input type="text" id="setting-api-url" class="config-input" placeholder="https://api.bookaroundme.com">
                    </div>
                    <div class="setting-row">
                        <label>Authorization:</label>
                        <input type="text" id="setting-api-auth" class="config-input" placeholder="API Key">
                    </div>
                    <div class="setting-row">
                        <label>Rule Set ID:</label>
                        <input type="text" id="setting-api-ruleset" class="config-input" placeholder="rule_set_...">
                    </div>
                    <div class="setting-row">
                        <label>Timeout (ms):</label>
                        <input type="number" id="setting-api-timeout" min="1000" max="30000" step="1000" value="10000">
                    </div>
                </div>

                <div class="settings-section">
                    <h4>Device-Konfiguration</h4>
                    <div class="setting-row">
                        <label>Project ID:</label>
                        <input type="text" id="setting-device-project" class="config-input" placeholder="project_...">
                    </div>
                    <div class="setting-row">
                        <label>Terminal ID:</label>
                        <input type="text" id="setting-device-terminal" class="config-input" placeholder="terminal_...">
                    </div>
                    <div class="setting-row">
                        <label>Area ID:</label>
                        <input type="text" id="setting-device-area" class="config-input" placeholder="area_...">
                    </div>
                </div>

                <div class="settings-section">
                    <h4>Aktionen</h4>
                    <div class="action-buttons">
                        <button id="test-audio" class="action-btn">Audio testen</button>
                        <button id="test-turnstile" class="action-btn">Turnstile testen</button>
                        <button id="save-config" class="action-btn success">Konfiguration speichern</button>
                        <button id="restart-system" class="action-btn danger">System neustarten</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modaler Dialog für Bestätigungen -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3 id="modal-title"></h3>
            <p id="modal-message"></p>
            <div class="modal-buttons">
                <button id="modal-ok" class="btn btn-primary">OK</button>
                <button id="modal-cancel" class="btn btn-secondary">Abbrechen</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>