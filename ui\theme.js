/**
 * Dynamisches Theme-System für die Ticket Control Station
 * Generiert Farbverläufe basierend auf einer konfigurierbaren Primärfarbe
 */

class ThemeManager {
    constructor() {
        this.defaultTheme = {
            primaryColor: '#667eea',
            successColor: '#3D8D7A',
            errorColor: '#D91656',
            gradientIntensity: 0.8,
            gradientAngle: 135
        };
    }

    /**
     * Konvertiert Hex-Farbe zu HSL
     * @param {string} hex - Hex-Farbwert (z.B. "#667eea")
     * @returns {Object} - {h, s, l} Werte
     */
    hexToHsl(hex) {
        // Entferne # falls vorhanden
        hex = hex.replace('#', '');
        
        // Konvertiere zu RGB
        const r = parseInt(hex.substr(0, 2), 16) / 255;
        const g = parseInt(hex.substr(2, 2), 16) / 255;
        const b = parseInt(hex.substr(4, 2), 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    /**
     * Konvertiert HSL zu Hex
     * @param {number} h - Hue (0-360)
     * @param {number} s - Saturation (0-100)
     * @param {number} l - Lightness (0-100)
     * @returns {string} - Hex-Farbwert
     */
    hslToHex(h, s, l) {
        h = h % 360;
        s = Math.max(0, Math.min(100, s)) / 100;
        l = Math.max(0, Math.min(100, l)) / 100;

        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h / 60) % 2 - 1));
        const m = l - c / 2;
        let r = 0, g = 0, b = 0;

        if (0 <= h && h < 60) {
            r = c; g = x; b = 0;
        } else if (60 <= h && h < 120) {
            r = x; g = c; b = 0;
        } else if (120 <= h && h < 180) {
            r = 0; g = c; b = x;
        } else if (180 <= h && h < 240) {
            r = 0; g = x; b = c;
        } else if (240 <= h && h < 300) {
            r = x; g = 0; b = c;
        } else if (300 <= h && h < 360) {
            r = c; g = 0; b = x;
        }

        r = Math.round((r + m) * 255);
        g = Math.round((g + m) * 255);
        b = Math.round((b + m) * 255);

        return "#" + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? "0" + hex : hex;
        }).join('');
    }

    /**
     * Generiert Komplementärfarbe
     * @param {string} primaryColor - Primärfarbe in Hex
     * @returns {string} - Komplementärfarbe in Hex
     */
    getComplementaryColor(primaryColor) {
        const hsl = this.hexToHsl(primaryColor);
        const complementaryHue = (hsl.h + 180) % 360;
        return this.hslToHex(complementaryHue, hsl.s, hsl.l);
    }

    /**
     * Generiert analoge Farbe
     * @param {string} primaryColor - Primärfarbe in Hex
     * @param {number} offset - Hue-Offset (-180 bis 180)
     * @returns {string} - Analoge Farbe in Hex
     */
    getAnalogousColor(primaryColor, offset = 30) {
        const hsl = this.hexToHsl(primaryColor);
        const analogousHue = (hsl.h + offset) % 360;
        return this.hslToHex(analogousHue, hsl.s, hsl.l);
    }

    /**
     * Generiert abgedunkelte Variante
     * @param {string} color - Farbe in Hex
     * @param {number} factor - Abdunklungsfaktor (0-1)
     * @returns {string} - Abgedunkelte Farbe in Hex
     */
    getDarkerVariant(color, factor = 0.2) {
        const hsl = this.hexToHsl(color);
        const darkerL = Math.max(0, hsl.l - (hsl.l * factor));
        return this.hslToHex(hsl.h, hsl.s, darkerL);
    }

    /**
     * Generiert aufgehellte Variante
     * @param {string} color - Farbe in Hex
     * @param {number} factor - Aufhellungsfaktor (0-1)
     * @returns {string} - Aufgehellte Farbe in Hex
     */
    getLighterVariant(color, factor = 0.2) {
        const hsl = this.hexToHsl(color);
        const lighterL = Math.min(100, hsl.l + ((100 - hsl.l) * factor));
        return this.hslToHex(hsl.h, hsl.s, lighterL);
    }

    /**
     * Generiert dunklere Variante der gleichen Farbe (monochrome Harmonie)
     * @param {string} primaryColor - Primärfarbe in Hex
     * @param {number} darkenFactor - Wie stark abdunkeln (0-1)
     * @returns {string} - Dunklere Variante in Hex
     */
    getMonochromeSecondary(primaryColor, darkenFactor = 0.4) {
        const hsl = this.hexToHsl(primaryColor);
        
        // Behalte gleichen Hue und Saturation, reduziere nur Lightness
        const secondaryLightness = Math.max(10, hsl.l - (hsl.l * darkenFactor));
        
        return this.hslToHex(hsl.h, hsl.s, secondaryLightness);
    }

    /**
     * Generiert sanfte Verlaufsfarbe durch leichte Ton-Verschiebung
     * @param {string} primaryColor - Primärfarbe in Hex
     * @returns {string} - Verlaufsfarbe in Hex
     */
    getGradientSecondary(primaryColor) {
        const hsl = this.hexToHsl(primaryColor);
        
        // Sehr kleine Hue-Verschiebung für subtilen Verlauf
        const gradientHue = (hsl.h + 15) % 360;
        
        // Leicht dunklere Variante für schönen Verlauf
        const gradientLightness = Math.max(15, hsl.l - 20);
        
        return this.hslToHex(gradientHue, hsl.s, gradientLightness);
    }

    /**
     * Generiert Theme-Palette basierend auf Primärfarbe
     * @param {Object} themeConfig - Theme-Konfiguration
     * @returns {Object} - Vollständige Farbpalette
     */
    generateThemePalette(themeConfig = {}) {
        const config = { ...this.defaultTheme, ...themeConfig };
        const primary = config.primaryColor;
        const success = config.successColor;
        const error = config.errorColor;
        
        // Monochrome Harmonie - nur verschiedene Schattierungen der gleichen Farbe
        const monochromeSecondary = this.getMonochromeSecondary(primary, 0.4);
        const gradientSecondary = this.getGradientSecondary(primary);
        
        // Standard Varianten
        const primaryDark = this.getDarkerVariant(primary, 0.3);
        const primaryLight = this.getLighterVariant(primary, 0.2);
        const primaryVeryDark = this.getDarkerVariant(primary, 0.5);
        
        // Erfolg/Fehler Varianten
        const successDark = this.getMonochromeSecondary(success, 0.3);
        const errorDark = this.getMonochromeSecondary(error, 0.3);
        
        // Analoge Farben (falls gewünscht)
        const analogous1 = this.getAnalogousColor(primary, 30);
        const analogous2 = this.getAnalogousColor(primary, -30);
        
        return {
            primary: primary,
            primaryLight: primaryLight,
            primaryDark: primaryDark,
            primaryVeryDark: primaryVeryDark,
            secondary: monochromeSecondary,
            secondaryDark: primaryVeryDark,
            gradientSecondary: gradientSecondary,
            success: success,
            successDark: successDark,
            error: error,
            errorDark: errorDark,
            analogous1: analogous1,
            analogous2: analogous2,
            
            // Dynamische Verläufe basierend auf Konfiguration
            gradients: {
                background: `linear-gradient(${config.gradientAngle}deg, ${primary} 0%, ${monochromeSecondary} 100%)`,
                backgroundAlt: `linear-gradient(${config.gradientAngle}deg, ${primaryLight} 0%, ${primaryDark} 100%)`,
                success: `linear-gradient(135deg, ${success}, ${successDark})`,
                error: `linear-gradient(135deg, ${error}, ${errorDark})`,
                warning: `linear-gradient(135deg, #FF9800, #F57C00)`,
                info: `linear-gradient(135deg, ${primaryLight}, ${primary})`,
                accent: `linear-gradient(135deg, ${primary}, ${primaryDark})`
            }
        };
    }

    /**
     * Wendet Theme auf die UI an
     * @param {Object} themeConfig - Theme-Konfiguration
     */
    applyTheme(themeConfig = {}) {
        const palette = this.generateThemePalette(themeConfig);
        
        // CSS Custom Properties setzen
        const root = document.documentElement;
        
        // Basis-Farben (monochrome Palette)
        root.style.setProperty('--primary-color', palette.primary);
        root.style.setProperty('--primary-light', palette.primaryLight);
        root.style.setProperty('--primary-dark', palette.primaryDark);
        root.style.setProperty('--primary-very-dark', palette.primaryVeryDark);
        root.style.setProperty('--secondary-color', palette.secondary);
        root.style.setProperty('--secondary-dark', palette.secondaryDark);
        root.style.setProperty('--gradient-secondary', palette.gradientSecondary);
        root.style.setProperty('--success-color', palette.success);
        root.style.setProperty('--success-dark', palette.successDark);
        root.style.setProperty('--error-color', palette.error);
        root.style.setProperty('--error-dark', palette.errorDark);
        root.style.setProperty('--analogous-1', palette.analogous1);
        root.style.setProperty('--analogous-2', palette.analogous2);
        
        // Verläufe
        root.style.setProperty('--gradient-background', palette.gradients.background);
        root.style.setProperty('--gradient-background-alt', palette.gradients.backgroundAlt);
        root.style.setProperty('--gradient-success', palette.gradients.success);
        root.style.setProperty('--gradient-error', palette.gradients.error);
        root.style.setProperty('--gradient-warning', palette.gradients.warning);
        root.style.setProperty('--gradient-info', palette.gradients.info);
        root.style.setProperty('--gradient-accent', palette.gradients.accent);
        
        console.log('Theme angewendet:', palette);
        return palette;
    }

    /**
     * Lädt Theme-Konfiguration von Server
     * @returns {Promise<Object>} - Theme-Konfiguration
     */
    async loadThemeConfig() {
        if (typeof require !== 'undefined') {
            // Electron-Umgebung
            try {
                const config = require('../config/settings.json');
                return config.ui.theme || this.defaultTheme;
            } catch (error) {
                console.warn('Theme-Konfiguration konnte nicht geladen werden:', error);
                return this.defaultTheme;
            }
        } else {
            // Browser-Umgebung (für Testing)
            return this.defaultTheme;
        }
    }

    /**
     * Initialisiert Theme-System
     */
    async initialize() {
        console.log('Initialisiere Theme-System...');
        
        try {
            const themeConfig = await this.loadThemeConfig();
            
            // Original-Konfiguration speichern für Reset
            this.originalConfig = themeConfig;
            this.currentConfig = themeConfig;
            
            const palette = this.applyTheme(themeConfig);
            
            console.log('Theme-System erfolgreich initialisiert');
            return palette;
        } catch (error) {
            console.error('Fehler bei Theme-Initialisierung:', error);
            
            // Fallback auf Standard-Theme
            this.originalConfig = this.defaultTheme;
            this.currentConfig = this.defaultTheme;
            const palette = this.applyTheme(this.defaultTheme);
            return palette;
        }
    }

    /**
     * Ändert Primärfarbe zur Laufzeit
     * @param {string} newPrimaryColor - Neue Primärfarbe in Hex
     */
    changePrimaryColor(newPrimaryColor) {
        const themeConfig = {
            ...this.defaultTheme,
            primaryColor: newPrimaryColor
        };
        
        return this.applyTheme(themeConfig);
    }

    /**
     * Vordefinierte Farbschemata
     */
    getPresetThemes() {
        return {
            'blue-purple': { primaryColor: '#667eea' }, // Standard
            'green-teal': { primaryColor: '#4CAF50' },
            'orange-red': { primaryColor: '#FF9800' },
            'pink-purple': { primaryColor: '#E91E63' },
            'cyan-blue': { primaryColor: '#00BCD4' },
            'indigo-purple': { primaryColor: '#3F51B5' },
            'red-orange': { primaryColor: '#F44336' },
            'teal-green': { primaryColor: '#009688' },
            'purple-pink': { primaryColor: '#9C27B0' },
            'deep-orange': { primaryColor: '#FF5722' }
        };
    }
}

// Global verfügbar machen
window.ThemeManager = ThemeManager;