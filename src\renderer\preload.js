const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sichere API für Renderer-Prozess
contextBridge.exposeInMainWorld('kioskAPI', {
  // API-Konfiguration
  getApiConfig: () => ipcRenderer.invoke('get-api-config'),
  
  // Zahlungs-API
  startPayment: (amount) => ipcRenderer.invoke('start-payment', amount),
  
  // Drucker-API
  printReceipt: (receiptData) => ipcRenderer.invoke('print-receipt', receiptData),
  printTicket: (ticketData) => ipcRenderer.invoke('print-ticket', ticketData),
  
  // System-Status
  getSystemStatus: () => ipcRenderer.invoke('get-system-status'),
  
  // Logging
  log: (level, message, data) => ipcRenderer.invoke('log-message', level, message, data)
});

// Flags zur Verfolgung, ob wir auf einem Screen bereits gehandelt haben
let cashBookingScreenHandled = false;
let dayEndScreenHandled = false;
let isCartBeingCleared = false;
let hasAutoReloaded = false;
let overlayVisible = false;
let pulsingEnabled = false;
let lastCartUpdateTime = 0;
let loadingOverlay = null;
let cartWasJustCleared = false; // Flag für Cart-Clear-Erkennung
let hasStartedSession = false; // Flag für Startbildschirm-Status

// Funktion zum Umgang mit dem Wechselgeld-Einbuchen-Screen
function handleCashBookingScreen() {
  // Prüfen, ob der Wechselgeld-Screen angezeigt wird
  const cashBookingTitle = document.querySelector('div.h-20.bg-slate-500.text-white.flex.flex-1.items-center.p-4.text-2xl.font-bold');
  
  if (cashBookingTitle && cashBookingTitle.textContent.trim().includes('Wechselgeld einbuchen')) {
    console.log('💰 Wechselgeld-Einbuchen-Screen erkannt');
    
    // Prüfen, ob wir diesen Screen bereits behandelt haben
    if (cashBookingScreenHandled) {
      console.log('ℹ️ Wechselgeld-Screen wurde bereits behandelt, überspringe');
      return;
    }
    
    // Nur für das 1-Cent-Feld die 1 eintragen
    const centContainers = document.querySelectorAll('div.divide-y.flex.flex-col');
    
    if (centContainers && centContainers.length > 0) {
      console.log(`🔢 ${centContainers.length} Münz/Schein-Container gefunden`);
      
      // Nach dem Container mit dem one_cent_euro Bild suchen
      let oneCentInputField = null;
      
      centContainers.forEach(container => {
        const img = container.querySelector('img[src="/assets/icons/cash/one_cent_euro.png"]');
        if (img) {
          oneCentInputField = container.querySelector('input[data-prevent-keyboard-focusin]');
          if (oneCentInputField) {
            console.log('💲 1-Cent-Eingabefeld gefunden, trage 1 ein');
            oneCentInputField.value = '1';
            oneCentInputField.dispatchEvent(new Event('input', { bubbles: true }));
            oneCentInputField.dispatchEvent(new Event('change', { bubbles: true }));
          }
        }
      });
      
      // Alle anderen Felder leer lassen oder mit 0 füllen
      const otherInputFields = document.querySelectorAll('input[data-prevent-keyboard-focusin]');
      otherInputFields.forEach(input => {
        if (input !== oneCentInputField) {
          // Leer lassen für die anderen Felder
          input.value = '';
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });
      
      // Markieren, dass dieser Screen behandelt wurde
      cashBookingScreenHandled = true;
      
      // Kurz warten und dann den Einbuchen-Button klicken
      setTimeout(() => {
        const bookButton = document.querySelector('button.flex.h-full.w-full.justify-center.items-center.text-white');
        
        if (bookButton && bookButton.textContent.includes('Einbuchen')) {
          console.log('✅ Klicke Einbuchen-Button');
          bookButton.click();
        } else {
          console.log('⚠️ Einbuchen-Button nicht gefunden');
          // Wenn Button nicht gefunden, Behandlung zurücksetzen für nächsten Versuch
          cashBookingScreenHandled = false;
        }
      }, 800); // Etwas längeres Timeout für die Verarbeitung
    }
  } else {
    // Wenn wir nicht mehr auf dem Wechselgeld-Screen sind, zurücksetzen für nächsten Besuch
    cashBookingScreenHandled = false;
  }
}

// Funktion zum Umgang mit dem Tagesabschluss-Screen
function handleDayEndScreen() {
  // Prüfen, ob der Tagesabschluss-Screen angezeigt wird
  const dayEndTitle = document.querySelector('div.p-4.text-white[class*="POS"]');
  
  if (dayEndTitle && dayEndTitle.textContent.trim().includes('TAGESABSCHLUSS')) {
    console.log('📅 Tagesabschluss/Kassensturz-Screen erkannt');
    
    // Prüfen, ob wir diesen Screen bereits behandelt haben
    if (dayEndScreenHandled) {
      console.log('ℹ️ Tagesabschluss-Screen wurde bereits behandelt, überspringe');
      return;
    }
    
    // Nur für das 1-Cent-Feld die 1 eintragen
    const centContainers = document.querySelectorAll('div.divide-y.flex.flex-col');
    
    if (centContainers && centContainers.length > 0) {
      console.log(`🔢 ${centContainers.length} Münz/Schein-Container gefunden`);
      
      // Nach dem Container mit dem one_cent_euro Bild suchen
      let oneCentInputField = null;
      
      centContainers.forEach(container => {
        const img = container.querySelector('img[src="/assets/icons/cash/one_cent_euro.png"]');
        if (img) {
          oneCentInputField = container.querySelector('input[data-prevent-keyboard-focusin]');
          if (oneCentInputField) {
            console.log('💲 1-Cent-Eingabefeld gefunden, trage 1 ein');
            oneCentInputField.value = '1';
            oneCentInputField.dispatchEvent(new Event('input', { bubbles: true }));
            oneCentInputField.dispatchEvent(new Event('change', { bubbles: true }));
          }
        }
      });
      
      // Alle anderen Felder leer lassen oder mit 0 füllen
      const otherInputFields = document.querySelectorAll('input[data-prevent-keyboard-focusin]');
      otherInputFields.forEach(input => {
        if (input !== oneCentInputField) {
          // Leer lassen für die anderen Felder
          input.value = '';
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });
      
      // Markieren, dass dieser Screen behandelt wurde
      dayEndScreenHandled = true;
      
      // 1 Sekunde warten und dann den Abschließen-Button klicken
      setTimeout(() => {
        // Alle Action-Buttons finden und durchsuchen
        const allButtons = document.querySelectorAll('button.wiz-action-button');
        console.log(`🔍 Gefundene Action-Buttons: ${allButtons.length}`);
        
        let foundButton = null;
        
        // Alle Buttons durchgehen und nach Text suchen (case-insensitive)
        allButtons.forEach(btn => {
          const buttonText = btn.textContent.trim().toUpperCase();
          console.log(`Button gefunden mit Text: "${buttonText}"`);
          
          if (buttonText.includes('ABSCHLIESSEN') || buttonText.includes('ABSCHLIEßEN')) {
            foundButton = btn;
            console.log('👍 ABSCHLIEßEN-Button identifiziert');
          }
        });
        
        if (foundButton) {
          // Prüfen, ob Button disabled ist
          if (foundButton.disabled) {
            console.log('⚠️ ABSCHLIEßEN-Button ist deaktiviert (disabled)');
            // Trotzdem versuchen zu klicken
            foundButton.disabled = false;
          }
          
          console.log('✅ Klicke ABSCHLIEßEN-Button');
          foundButton.click();
          
          // Versuche auch direkt Event auszulösen für bessere Kompatibilität
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
          });
          foundButton.dispatchEvent(clickEvent);
        } else {
          console.log('⚠️ ABSCHLIEßEN-Button nicht gefunden');
          // Wenn Button nicht gefunden, Behandlung zurücksetzen für nächsten Versuch
          dayEndScreenHandled = false;
        }
      }, 1000); // 1 Sekunde Verzögerung nach Eingabe des 1-Cent-Werts
    }
  } else {
    // Wenn wir nicht mehr auf dem Tagesabschluss-Screen sind, zurücksetzen für nächsten Besuch
    dayEndScreenHandled = false;
  }
}

// Starte regelmäßige Überprüfung auf spezielle Screens
function startCashBookingMonitor() {
  console.log('🔍 Starte Überwachung für spezielle Screens');
  
  // Sofort prüfen
  handleCashBookingScreen();
  handleDayEndScreen();
  
  // Regelmäßige Überprüfung alle 2 Sekunden
  const monitorInterval = setInterval(() => {
    handleCashBookingScreen();
    handleDayEndScreen();
  }, 2000);
  
  return monitorInterval;
}

// Erkennen von Rückkehr zum Produkt-Screen nach Checkout
function startProductScreenMonitor() {
  console.log('🔄 Starte Überwachung für Rückkehr zum Produkt-Screen');
  
  // Zustandsvariable für Checkout-Erkennung
  let checkoutState = {
    isInCheckout: false,      // Sind wir im Checkout-Bereich
    hasBeenInCheckout: false, // Waren wir im Checkout
    lastUrl: window.location.href, // Aktuelle URL zum Vergleich
    urlChangedAfterCheckout: false, // URL-Änderung nach Checkout
    shopUrlPattern: /\/shop_/  // URL-Muster für Shop-Seite
  };
  
  // Regelmäßige Überprüfung alle 2 Sekunden
  const monitorInterval = setInterval(() => {
    // Aktuelle URL prüfen
    const currentUrl = window.location.href;
    const isShopUrl = checkoutState.shopUrlPattern.test(currentUrl);
    
    // URL-Änderung erkannt
    if (currentUrl !== checkoutState.lastUrl) {
      console.log(`🔄 URL-Änderung: ${currentUrl} (Shop-URL: ${isShopUrl ? 'Ja' : 'Nein'})`);
      
      // Wenn wir im Checkout waren und jetzt die URL sich ändert
      if (checkoutState.hasBeenInCheckout) {
        checkoutState.urlChangedAfterCheckout = true;
        console.log('📦 URL-Änderung nach Checkout erkannt - Wahrscheinlich zurück zum Shop');
      }
      
      checkoutState.lastUrl = currentUrl;
    }
    
    // 1. Checkout-Status erkennen
    const checkoutElement = document.querySelector('app-checkout');
    
    if (checkoutElement) {
      if (!checkoutState.isInCheckout) {
        console.log('🛒 Checkout-Screen erkannt');
      }
      checkoutState.isInCheckout = true;
      checkoutState.hasBeenInCheckout = true; // Merken dass wir im Checkout waren
      
    } else {
      // Nicht mehr im Checkout
      if (checkoutState.isInCheckout) {
        console.log('💰 Checkout-Bereich verlassen');
        
        // Sofort prüfen, ob wir zuvor im Checkout waren und ob Produktkarten angezeigt werden
        if (checkoutState.hasBeenInCheckout) {
          console.log('💼 Prüfe auf Rückkehr zum Shop nach Checkout...');
          checkForBackToShopAndReload();
        }
      }
      checkoutState.isInCheckout = false;
    }
    
    // Wenn URL sich nach Checkout geändert hat, prüfe auf Shop-Rückkehr
    if (checkoutState.urlChangedAfterCheckout) {
      console.log('💼 Prüfe auf Rückkehr zum Shop nach URL-Änderung...');
      checkForBackToShopAndReload();
    }
    
  }, 2000); // Alle 2 Sekunden prüfen
  
  // Hilfsfunktion zum Prüfen auf Shop-Rückkehr und Reload
  function checkForBackToShopAndReload() {
    // Prüfen auf verschiedene Shop-Indikatoren
    const productCards = document.querySelectorAll('div.product-card');
    const productElements = document.querySelectorAll('[class*="product-"]');
    const appShop = document.querySelector('app-shop');
    const noCheckout = !document.querySelector('app-checkout');
    
    console.log(`🔍 Erkenne: ${productCards.length} Produktkarten, ${productElements.length} Produkt-Elemente, Shop-App: ${appShop ? 'Ja' : 'Nein'}, Kein Checkout: ${noCheckout ? 'Ja' : 'Nein'}`);
    
    // Wenn wir Produkt-Elemente haben und nicht mehr im Checkout sind
    if (productElements.length > 0 && noCheckout) {
      console.log('✅ Rückkehr zum Shop erkannt!');
      
      // Verzögere den Reload um die Seite richtig anzuzeigen
      console.log('📵 Bereite Seiten-Reload vor (in 1 Sekunde)...');
      
      setTimeout(() => {
        // Versuche zuerst, den Warenkorb zu leeren
        try {
          console.log('🛍️ Versuche, Warenkorb vor Reload zu leeren...');
          clearCart();
        } catch (err) {
          console.error('Fehler beim Leeren des Warenkorbs:', err);
        }
        
        // Reload nach Versuch, Warenkorb zu leeren
        setTimeout(() => {
          console.log('🔄 RELOAD: Kehre zurück zur Startseite');
          window.location.reload();
          
          // Reset des Status
          checkoutState.hasBeenInCheckout = false;
          checkoutState.urlChangedAfterCheckout = false;
        }, 500);
      }, 1000);
    }
  }
  
  // Hilfsfunktion zum Leeren des Warenkorbs
  function clearCart() {
    // Suche nach dem Löschen-Button oder einer Möglichkeit, den Warenkorb zu leeren
    const clearButtons = document.querySelectorAll('button.clear-cart, button.empty-cart, button[title="Clear"], button[title="Empty"]');
    if (clearButtons && clearButtons.length > 0) {
      console.log('🗑️ Warenkorb-Leeren-Button gefunden, klicke...');
      clearButtons[0].click();
    }
  }
  
  return monitorInterval;
}

// Direkt starten ohne auf kioskAPI zu warten
function startKioskFunctions() {
  console.log('Kiosk Preload: Starte Hochformat-Transformation (ohne API)');
  
  try {
    // Sofort die kritischen Funktionen anwenden
    applyKioskStyles();
    setupKioskBehavior();
    hideUnnecessaryPaymentOptions();
    startOpenTransactionMonitor();
    startCashBookingMonitor();
    startProductScreenMonitor(); // Überwachung für Rückkehr zum Produkt-Screen
    
    // Einmaliger Check nach Angular-Ladezeit (nur wenn nötig)
    setTimeout(() => {
      console.log('🔄 Finale Kiosk-Initialisierung nach Angular-Ladezeit');
      
      // Nur noch einmal prüfen ob Styles korrekt angewendet sind
      const kioskStylesApplied = document.getElementById('kiosk-hochformat-styles');
      if (!kioskStylesApplied) {
        console.log('📋 Styles nicht gefunden - wende erneut an');
        applyKioskStyles();
      }
      
      // Finale Prüfung der spezifischen Screens
      handleCashBookingScreen();
      handleDayEndScreen();
    }, 3000); // Nur ein einziger Check nach 3 Sekunden
    
  } catch (error) {
    console.error('Fehler bei Kiosk-Funktionen:', error);
  }
}

// Auto-Login Funktion
function attemptAutoLogin() {
  console.log('🔐 Prüfe auf Login-Screen...');
  
  // Prüfe ob Login-Screen vorhanden ist
  const loginScreen = document.querySelector('app-login');
  if (!loginScreen) {
    console.log('Kein Login-Screen gefunden');
    return;
  }
  
  console.log('✅ Login-Screen gefunden, starte Auto-Login');
  
  // Suche Login-Felder
  const loginIdInput = document.querySelector('input[formcontrolname="login_id"]');
  const pinInput = document.querySelector('input[formcontrolname="pin"]');
  const loginButton = document.querySelector('button.loginButton');
  
  if (!loginIdInput || !pinInput || !loginButton) {
    console.log('❌ Login-Felder nicht gefunden');
    return;
  }
  
  console.log('🔍 Login-Felder gefunden, fülle aus...');
  
  // Werte setzen
  loginIdInput.value = '1234';
  loginIdInput.dispatchEvent(new Event('input', { bubbles: true }));
  loginIdInput.dispatchEvent(new Event('change', { bubbles: true }));
  
  pinInput.value = '654321';
  pinInput.dispatchEvent(new Event('input', { bubbles: true }));
  pinInput.dispatchEvent(new Event('change', { bubbles: true }));
  
  console.log('✅ Login-Daten eingetragen');
  
  // Warte kurz, dann klicke Login-Button
  setTimeout(() => {
    if (loginButton && !loginButton.disabled) {
      console.log('🔑 Klicke Login-Button');
      loginButton.click();
      
      // Zusätzlicher Event-Click für Sicherheit
      setTimeout(() => {
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
          button: 0
        });
        loginButton.dispatchEvent(clickEvent);
        console.log('Zusätzlicher Login Event-Click ausgeführt');
      }, 100);
    } else {
      console.log('❌ Login-Button disabled oder nicht gefunden');
    }
  }, 500);
}

// DOM-Ready Handler
document.addEventListener('DOMContentLoaded', () => {
  console.log('Kiosk Preload: DOM geladen, starte sofort');
  
  // Auto-Login versuchen
  setTimeout(attemptAutoLogin, 1000);
  
  // Falls Login-Screen später erscheint, nochmal versuchen
  setTimeout(attemptAutoLogin, 3000);
  
  startKioskFunctions();
});

// Falls DOM bereits geladen ist
if (document.readyState !== 'loading') {
  console.log('Kiosk Preload: DOM bereits geladen, starte sofort');
  
  // Auto-Login versuchen
  setTimeout(attemptAutoLogin, 1000);
  setTimeout(attemptAutoLogin, 3000);
  
  startKioskFunctions();
}

function applyKioskStyles() {
  console.log('Wende Hochformat-Styles an');
  
  // Prüfe ob bereits ein Style-Element existiert
  const existingStyle = document.getElementById('kiosk-hochformat-styles');
  if (existingStyle) {
    console.log('Style-Element bereits vorhanden, entferne es');
    existingStyle.remove();
  }
  
  const css = `
    /* KIOSK-OVERLAY: Verstecke Original nur auf Shop-Seite, NICHT bei Checkout */
    body:has(app-shop):not(:has(app-checkout)) > app-root {
      display: none !important;
    }

    /* Sobald app-checkout da ist, Original-Interface zeigen */
    body:has(app-checkout) > app-root {
      display: block !important;
    }

    /* Kiosk-Overlay verstecken bei Checkout */
    body:has(app-checkout) #kiosk-overlay {
      display: none !important;
    }

    /* CHECKOUT KIOSK DESIGN - Verstecke Original komplett */
    body:has(app-checkout) app-root {
      display: none !important;
    }

    /* Kiosk Overlay Styles */
    #kiosk-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: #455A84FF;
      z-index: 10000;
      font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      letter-spacing: 1.6px;
    }

    #kiosk-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    /* Tab-Navigation oben */
    #tab-navigation {
      flex: 0 0 auto;
      background: #1f2937;
      padding: 0.5rem 1rem;
      border-bottom: 2px solid #374151;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    #tab-container {
      display: flex;
      gap: 0.75rem;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 0.5rem 0;
      scrollbar-width: none;
      -ms-overflow-style: none;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch; /* Smooth scrolling auf Touch-Geräten */
      position: relative;
    }

    #tab-container::-webkit-scrollbar {
      display: none;
    }

    /* Scroll-Indikatoren für bessere UX */
    #tab-navigation::before,
    #tab-navigation::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 20px;
      pointer-events: none;
      z-index: 10;
      transition: opacity 0.3s ease;
    }

    #tab-navigation::before {
      left: 0;
      background: linear-gradient(to right, #1f2937, transparent);
      opacity: 0;
    }

    #tab-navigation::after {
      right: 0;
      background: linear-gradient(to left, #1f2937, transparent);
      opacity: 1;
    }

    #tab-navigation.can-scroll-left::before {
      opacity: 1;
    }

    #tab-navigation.can-scroll-right::after {
      opacity: 1;
    }

    #tab-navigation:not(.can-scroll-right)::after {
      opacity: 0;
    }

    .tab-button {
      flex: 0 0 auto;
      background: #374151;
      color: #d1d5db;
      border: none;
      padding: 1rem 2rem;
      border-radius: 0.75rem;
      font-weight: 600;
      font-size: 1.1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      min-width: fit-content;
      border: 2px solid transparent;
      touch-action: manipulation; /* Verhindert Zoom bei Touch */
      user-select: none;
      -webkit-tap-highlight-color: transparent; /* Entfernt blauen Highlight auf iOS */
      min-height: 48px; /* Mindestgröße für Touch-Targets */
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .tab-button:hover {
      background: #4b5563;
      color: #f3f4f6;
      transform: translateY(-1px);
    }

    .tab-button.active {
      background: #10b981;
      color: #ffffff;
      border-color: #059669;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .tab-button.active:hover {
      background: #059669;
      transform: translateY(-1px);
    }

    /* Produktbereich */
    #product-area {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      background: #374151;
      perspective: 1000px;
      perspective-origin: center;
      scroll-behavior: smooth;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
      background-image: linear-gradient(135deg, #485566FF 0%, #334155 100%);
      position: relative;
      z-index: 0;
    }

    #product-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr); /* Zwei exakt gleiche Spalten */
      gap: 25px !important;  /* Gleicher Abstand in alle Richtungen */
      transform-style: preserve-3d;
      padding: 25px !important; /* Gleicher Abstand zum Rand */
      max-width: 100%;
      margin: 0 auto;
      box-sizing: border-box;
      grid-auto-rows: 140px; /* Alle Zeilen mit fester Höhe */
    }

    /* Base card styles */
    .product-card {
      background: transparent;
      border: 2px solid #334155;
      border-radius: 12px;
      padding: 1rem;
      height: 140px; /* Feste Höhe für alle Karten */
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      cursor: pointer;
      transition: all 0.25s ease;
      will-change: transform, opacity;
      transform: scale(1);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden; /* Verhindert Überlauf von Inhalten */
    }
    
    /* Garantiert einheitliche Karten-Größen */
    .product-card {
      transform-origin: center;
      margin: 0 auto;
      width: 100%;
      box-sizing: border-box !important;
      max-width: 100%;
      flex-grow: 0;
      flex-shrink: 0;
      height: 140px !important; /* Erzwingt einheitliche Höhe */
    }
    
    /* Verhindert, dass die letzte Karte anders aussieht */
    .product-card:last-child {
      margin-bottom: 0;
      height: 140px !important;
      width: 100% !important;
    }

    .product-card:hover {
      transform: scale(1.05);
    }

    .product-card.available {
      border-color: #10b981;
      background: #10b981;
      color: white;
    }
    
    /* Inaktive Produktkarten */
    .product-card:not(.available) {
      color: #273241FF;
    }

    .product-title {
      font-weight: 600;
      font-size: 1.6rem;
      margin-bottom: 0.0rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .product-price {
      font-size: 2.2rem;
      font-weight: 700;
      text-align: right;
    }

    /* Warenkorb + Checkout unten - FIXE HÖHE */
    #cart-checkout-area {
      flex: 0 0 25vh;
      background: #8D8DAA;
      color: white;
      display: flex;
      flex-direction: column;
      min-height: 25vh;
      max-height: 25vh;
      box-shadow: 0 -2px 20px rgba(23, 29, 38, 1);
      z-index: 2;
      position: relative;
      border-top: 1px solid #2B3543FF;
      background-image: linear-gradient(135deg, #334155 0%, #273241FF 100%);
    }

    #cart-summary {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
      min-height: 0;
      max-height: calc(25vh - 80px);
      border-top: 2px solid ##62748B;
    }

    .cart-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 2.0rem;
      padding: 0.5rem 0;
      border-bottom: 1px solid #4b5563;
      flex-shrink: 0;
      min-height: 40px;
    }
    
    .cart-empty {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      font-size: 2rem;
      font-weight: medium;
      color: #626D7CFF;
      text-align: center;
    }

    /* Button Container für nebeneinander liegende Buttons */
    #button-container {
      display: flex;
      height: 100px;
      gap: 0;
    }

    #checkout-button {
      flex: 3;
      background: #059669;
      color: white;
      border: none;
      font-size: 2.6rem;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.15s ease;
      border-radius: 0;
    }
    
    /* Pulsierende Animation für den Checkout-Button */
    @keyframes pulse {
      0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7); }
      50% { transform: scale(1.03); box-shadow: 0 0 0 10px rgba(5, 150, 105, 0); }
      100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(5, 150, 105, 0); }
    }
    
    .pulse-animation {
      animation: pulse 2s infinite;
    }

    #checkout-button:hover {
      background: #047857;
    }

    #checkout-button:disabled {
      background: #059669;
      cursor: not-allowed;
    }

    #clear-cart-button {
      flex: 1;
      background: #dc2626;
      color: white;
      border: none;
      font-size: 1.5rem;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.15s ease;
      border-radius: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    #clear-cart-button:hover {
      background: #b91c1c;
    }

    #clear-cart-button:disabled {
      background: #dc2626;
      cursor: not-allowed;
    }

    /* Produkt-Bereich Scrollbar ausblenden */
    #product-area::-webkit-scrollbar {
      width: 0;
      display: none;
    }

    /* Scrollbar Styling für Warenkorb */
    #cart-summary::-webkit-scrollbar {
      width: 8px;
    }

    #cart-summary::-webkit-scrollbar-track {
      background: #4b5563;
      border-radius: 4px;
    }

    #cart-summary::-webkit-scrollbar-thumb {
      background: #6b7280;
      border-radius: 4px;
    }

    #cart-summary::-webkit-scrollbar-thumb:hover {
      background: #9ca3af;
    }

    /* Checkout-Warenkorb Scrollbar (gleich wie Hauptscreen) */
    #checkout-cart-scroll::-webkit-scrollbar {
      width: 8px;
    }

    #checkout-cart-scroll::-webkit-scrollbar-track {
      background: #4b5563;
      border-radius: 4px;
    }

    #checkout-cart-scroll::-webkit-scrollbar-thumb {
      background: #6b7280;
      border-radius: 4px;
    }

    #checkout-cart-scroll::-webkit-scrollbar-thumb:hover {
      background: #9ca3af;
    }

    /* Payment Input Area */
    body:has(app-checkout) app-pos-currency-input {
      background: white !important;
      border-radius: 8px !important;
      margin-bottom: 0.5rem !important;
    }

    body:has(app-checkout) app-pos-currency-input input {
      font-size: 1.5rem !important;
      padding: 1rem !important;
      font-weight: bold !important;
    }

    body:has(app-checkout) app-pos-currency-input label span {
      font-size: 1rem !important;
      font-weight: 600 !important;
      color: #374151 !important;
    }

    /* Numeric Keyboard */
    body:has(app-checkout) app-numeric-keyboard {
      background: #e5e7eb !important;
      border-radius: 12px !important;
      padding: 1rem !important;
    }

    body:has(app-checkout) app-numeric-keyboard .keyboard-button {
      background: white !important;
      border: 2px solid #d1d5db !important;
      border-radius: 8px !important;
      font-size: 1.5rem !important;
      font-weight: bold !important;
      min-height: 60px !important;
      color: #374151 !important;
      transition: all 0.15s ease !important;
    }

    body:has(app-checkout) app-numeric-keyboard .keyboard-button:hover {
      background: #f3f4f6 !important;
      transform: scale(0.98) !important;
    }

    body:has(app-checkout) app-numeric-keyboard .keyboard-button:active {
      background: #e5e7eb !important;
      transform: scale(0.95) !important;
    }

    /* Payment Options */
    body:has(app-checkout) app-payment-options {
      background: #059669 !important;
      padding: 1rem !important;
      border-radius: 12px 12px 0 0 !important;
    }

    body:has(app-checkout) app-payment-options button {
      background: #047857 !important;
      color: white !important;
      border: 2px solid #065f46 !important;
      border-radius: 8px !important;
      font-size: 1.3rem !important;
      font-weight: bold !important;
      min-height: 80px !important;
      transition: all 0.15s ease !important;
    }

    body:has(app-checkout) app-payment-options button:hover {
      background: #065f46 !important;
      transform: scale(0.98) !important;
    }

    /* Footer verstecken */
    body:has(app-checkout) app-information-footer {
      display: none !important;
    }

    /* Alphabetic Keyboard verstecken */
    body:has(app-checkout) app-alphabetic-keyboard {
      display: none !important;
    }

    /* Rückgeld/Differenz Bereich */
    body:has(app-checkout) form > div:last-child {
      background: white !important;
      border-radius: 8px !important;
      font-size: 1.2rem !important;
    }

    body:has(app-checkout) form > div:last-child span {
      font-size: 1rem !important;
      font-weight: 600 !important;
      color: #374151 !important;
    }

    body:has(app-checkout) form > div:last-child > div {
      font-size: 1.5rem !important;
      font-weight: bold !important;
      color: #dc2626 !important;
    }

    /* Verstecke unnötige Elemente */
    body:has(app-checkout) [class*="Betrag erhalten"] {
      display: none !important;
    }

    body:has(app-checkout) [class*="Rückgeld"] {
      display: none !important;
    }

    /* ADDON AUTO-SKIP: Verstecke Addon-Seiten komplett */
    body:has(app-product-wizard) app-product-wizard {
      display: none !important;
    }

    body:has(app-addons) app-addons {
      display: none !important;
    }

    /* =========================== */
    /* BUTTON MORPHING EFFECTS     */
    /* =========================== */
    
    /* Ripple Effect Container */
    .button-ripple {
      position: relative;
      overflow: hidden;
      transform: translateZ(0); /* Hardware acceleration */
    }
    
    /* Ripple Animation Circle */
    @keyframes button-ripple {
      0% {
        transform: scale(0);
        opacity: 0.6;
      }
      100% {
        transform: scale(2);
        opacity: 0;
      }
    }
    
    /* Button Click Scale Effect */
    @keyframes button-press {
      0% {
        transform: scale(1);
      }
      30% {
        transform: scale(0.96);
      }
      60% {
        transform: scale(0.94);
      }
      100% {
        transform: scale(1);
      }
    }
    
    /* Button Bounce Effect for Success Actions */
    @keyframes button-bounce {
      0% {
        transform: scale(1);
      }
      20% {
        transform: scale(1.08);
      }
      40% {
        transform: scale(1.12);
      }
      60% {
        transform: scale(0.98);
      }
      75% {
        transform: scale(1.03);
      }
      90% {
        transform: scale(0.99);
      }
      100% {
        transform: scale(1);
      }
    }
    
    /* Smooth Pulse Effect for Checkout Pay Button */
    @keyframes smooth-pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
      }
      25% {
        transform: scale(1.015);
        box-shadow: 0 0 0 5px rgba(5, 150, 105, 0.5);
      }
      50% {
        transform: scale(1.03);
        box-shadow: 0 0 0 10px rgba(5, 150, 105, 0.3);
      }
      75% {
        transform: scale(1.015);
        box-shadow: 0 0 0 5px rgba(5, 150, 105, 0.1);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
      }
    }
    
    /* Apply morphing to all interactive buttons */
    .product-card,
    #checkout-button,
    #clear-cart-button,
    #start-session-button,
    #checkout-pay-button,
    #checkout-clear-cart-button,
    #voucher-add-button {
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      will-change: transform, box-shadow;
    }
    
    /* Pressed state animation classes */
    .button-pressed {
      animation: button-press 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .button-bounced {
      animation: button-bounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
    
    /* Enhanced hover states for better feedback */
    .product-card:hover {
      transform: scale(1.05) translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transition-duration: 0.2s; /* Faster hover response */
    }
    
    #checkout-button:hover {
      transform: scale(1.03);
      box-shadow: 0 0 25px rgba(5, 150, 105, 0.5);
      transition-duration: 0.2s;
    }
    
    /* Smooth Pulse Animation Class */
    .button-pulse {
      animation: smooth-pulse 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    }
    
    /* Apply continuous pulse to pay button when visible */
    #checkout-pay-button {
      animation: smooth-pulse 2.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    }
    
    /* Apply smooth pulse to EC card payment button */
    body:has(app-checkout) app-payment-options .flex.justify-stretch button:has(.fa-credit-card) {
      animation: smooth-pulse 2.3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    }
    
    #clear-cart-button:hover {
      transform: scale(1.03);
      box-shadow: 0 0 25px rgba(220, 38, 38, 0.5);
      transition-duration: 0.2s;
    }
    
    /* Smooth back transition on mouse leave */
    .product-card:not(:hover),
    #checkout-button:not(:hover),
    #clear-cart-button:not(:hover) {
      transition-duration: 0.4s;
    }
  `;
  
  const styleElement = document.createElement('style');
  styleElement.textContent = css;
  styleElement.id = 'kiosk-hochformat-styles';
  document.head.appendChild(styleElement);
  
  console.log('Hochformat-Styles hinzugefügt, Style-Element:', styleElement);
  
  // Warte auf Shop-Seite bevor Overlay erstellt wird
  waitForShopAndCreateOverlay();
  
  // Force-Reload aller Daten nach Seiten-Reload
  if (document.readyState === 'complete') {
    console.log(' Seite bereits geladen - force reload aller Daten');
    setTimeout(() => {
      console.log(' Force-reload: Lade alle Daten neu');
      loadOriginalData();
    }, 2000);
  }
  
  // MutationObserver für dynamische DOM-Änderungen
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // Prüfe auf Produktkarten-spezifische Änderungen
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // Element node
            const element = node;
            if (element.tagName && 
                (element.tagName.toLowerCase().includes('app-') || 
                 element.className.includes('product') ||
                 element.className.includes('grid'))) {
              console.log('Neue Komponente erkannt:', element.tagName, element.className);
              setTimeout(() => findAndStyleProductGrid(), 500);
            }
          }
        });
      }
    });
  });
  
  // Observer starten
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Sofort nach dem CSS auch direkt suchen
  setTimeout(() => {
    findAndStyleProductGrid();
  }, 100);
  
  // Intervall für kontinuierliche Überwachung bis Produktkarten geladen sind
  let productCheckInterval = setInterval(() => {
    console.log('Intervall-Check für Produktkarten...');
    const hasProducts = document.querySelectorAll('[class*="product"]').length > 0 ||
                       document.querySelectorAll('app-product-listing').length > 0 ||
                       document.querySelectorAll('app-product-listing-item').length > 0;
    
    if (hasProducts) {
      console.log('Produktkarten erkannt, stoppe Intervall');
      clearInterval(productCheckInterval);
      findAndStyleProductGrid();
    } else {
      findAndStyleProductGrid(); // Weiter nach Grids suchen
    }
  }, 2000);
  
  // Optional: Log an Main Process (falls kioskAPI verfügbar)
  if (window.kioskAPI && window.kioskAPI.log) {
    window.kioskAPI.log('info', 'Hochformat-Styles angewendet');
  }
}

function findAndStyleProductGrid() {
  console.log('=== DETAILLIERTE DOM-ANALYSE ===');
  
  // Komplette DOM-Struktur loggen
  console.log('Document body classes:', document.body.className);
  
  // Alle Elemente mit "col-span" finden
  const colSpanElements = document.querySelectorAll('[class*="col-span"]');
  console.log(`Gefundene col-span Elemente: ${colSpanElements.length}`);
  
  colSpanElements.forEach((element, index) => {
    console.log(`Col-span ${index + 1}:`, element.className, element.tagName);
    console.log('  Inhalt:', element.innerHTML.substring(0, 200) + '...');
  });
  
  // Alle Grid-Elemente mit detaillierten Infos
  const allGrids = document.querySelectorAll('.grid');
  console.log(`Gefundene Grid-Elemente: ${allGrids.length}`);
  
  allGrids.forEach((grid, index) => {
    const classes = grid.className;
    const parent = grid.parentElement;
    console.log(`Grid ${index + 1}:`);
    console.log('  Classes:', classes);
    console.log('  Parent:', parent ? parent.className : 'kein parent');
    console.log('  Children count:', grid.children.length);
    console.log('  Computed styles:', {
      display: getComputedStyle(grid).display,
      gridTemplateColumns: getComputedStyle(grid).gridTemplateColumns,
      width: getComputedStyle(grid).width
    });
    
    // Forciere 2-Spalten Layout nur für Produktgrids, NICHT für Numpad
    if (classes.includes('grid-cols') && !classes.includes('keyboard-theme')) {
      console.log('  -> Wende 2-Spalten Layout an');
      
      grid.style.setProperty('display', 'grid', 'important');
      grid.style.setProperty('grid-template-columns', 'repeat(2, 1fr)', 'important');
      grid.style.setProperty('gap', '0.5rem', 'important');
      grid.style.setProperty('width', '100%', 'important');
      grid.style.setProperty('padding', '0.5rem', 'important');
      grid.style.setProperty('box-sizing', 'border-box', 'important');
      
      // Alle direkten div-Kinder anpassen
      Array.from(grid.children).forEach((child, childIndex) => {
        if (child.tagName === 'DIV') {
          console.log(`    -> Kind ${childIndex + 1}:`, child.className);
          child.style.setProperty('width', '100%', 'important');
          child.style.setProperty('min-height', '100px', 'important');
        }
      });
    }
    
    // Spezielle Behandlung für Numpad
    if (classes.includes('keyboard-theme')) {
      console.log('  -> Numpad gefunden, behalte 4-Spalten Layout');
      grid.style.setProperty('grid-template-columns', 'repeat(4, 1fr)', 'important');
      grid.style.setProperty('gap', '5px', 'important');
      grid.style.setProperty('overflow', 'visible', 'important');
      grid.style.setProperty('height', 'auto', 'important');
    }
    
    // Footer mit "Willkommen zurück" verstecken
    if (classes.includes('bg-slate-500') && grid.textContent.includes('Willkommen zurück')) {
      console.log('  -> Verstecke Willkommen-zurück Footer');
      grid.style.setProperty('display', 'none', 'important');
    }
    
    // Footer mit Grid-Icon (fa-grid-2) verstecken
    if (classes.includes('bg-slate-500') && grid.querySelector('.fa-grid-2')) {
      console.log('  -> Verstecke Footer mit Grid-Icon');
      grid.style.setProperty('display', 'none', 'important');
    }
    
    // Footer mit Willkommen zurück verstecken (letzter Footer)
    if (classes.includes('bg-slate-500') && classes.includes('grid-cols-6') && 
        grid.textContent.includes('Willkommen zurück')) {
      console.log('  -> Verstecke letzten Footer');
      grid.style.setProperty('display', 'none', 'important');
    }
  });
  
  // App-spezifische Elemente
  const appElements = document.querySelectorAll('[class*="app-"]');
  console.log(`Gefundene app-* Elemente: ${appElements.length}`);
  
  // Produktlisting-spezifische Suche
  const productElements = document.querySelectorAll('[class*="product"]');
  console.log(`Gefundene product-* Elemente: ${productElements.length}`);
  
  productElements.forEach((element, index) => {
    console.log(`Product ${index + 1}:`, element.className, element.tagName);
  });
  
  // Spezifische Checkout-Bereiche ausblenden
  hideCheckoutElements();
  
  
  console.log('=== ENDE DOM-ANALYSE ===');
}

function waitForShopAndCreateOverlay() {
  console.log('Warte auf Shop-Seite...');
  
  const checkForShop = () => {
    const shopElement = document.querySelector('app-shop');
    if (shopElement) {
      console.log('Shop-Seite gefunden');
      
      // Prüfe ob Session bereits gestartet wurde
      if (hasStartedSession) {
        console.log('Session bereits gestartet, erstelle Kiosk-Overlay');
        createKioskOverlay();
      } else {
        console.log('Neue Session, zeige Startbildschirm');
        createStartScreen();
      }
    } else {
      console.log('Shop-Seite noch nicht da, warte weitere 1s');
      setTimeout(checkForShop, 1000);
    }
  };
  
  // Sofort prüfen
  checkForShop();
}

function createStartScreen() {
  console.log(' Erstelle Willkommens-Startbildschirm');
  
  // Prüfe ob bereits vorhanden
  if (document.getElementById('start-screen-overlay')) {
    console.log('Startbildschirm bereits vorhanden');
    return;
  }
  
  const startOverlay = document.createElement('div');
  startOverlay.id = 'start-screen-overlay';
  startOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    z-index: 10000;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    overflow: hidden;
  `;
  
  startOverlay.innerHTML = `
    <!-- Video-Hintergrund -->
    <video 
      autoplay 
      loop 
      muted 
      playsinline
      style="
        position: absolute;
        top: 50%;
        left: 50%;
        min-width: 100%;
        min-height: 100%;
        width: auto;
        height: auto;
        transform: translate(-50%, -50%);
        z-index: -2;
        object-fit: cover;
      "
    >
      <source src="kiosk-asset://video.mp4" type="video/mp4">
      <!-- Fallback für Browser die das Video nicht unterstützen -->
    </video>
    
    <!-- Transparentes Anthrazit-Overlay -->
    <div style="
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(55, 65, 81, 0.7);
      backdrop-filter: blur(2px);
      z-index: -1;
    "></div>
    
    <!-- Hauptinhalt Card -->
    <div style="
      position: relative;
       text-align: center;
       max-width: 550px;
       padding: 3rem;
      background: rgba(51, 65, 85, 0.9);
      border-radius: 20px;
      backdrop-filter: blur(15px);
      box-shadow: 0 2px 20px rgba(23, 29, 38, 1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      z-index: 1;
    ">
      
      <!-- Haupttitel -->
      <h1 style="
        font-size: 3.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        letter-spacing: 2px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      ">
        DER SPASS BEGINNT HIER!
      </h1>
      
      <!-- Untertitel -->
      <p style="
        font-size: 1.8rem;
        margin: 0 0 3rem 0;
        opacity: 0.9;
        font-weight: 300;
        line-height: 0.4;
      ">
    
        <br>
        
      </p>
      
      <!-- Starten Button -->
      <button id="start-session-button" style="
        background: #10b981;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 2.0rem 4rem;
        font-size: 2.8rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
        letter-spacing: 1px;
        min-width: 93%;
      ">
        JETZT TICKETS 
        <br>
        KAUFEN
      </button>
      

    </div>
  `;
  
  document.body.appendChild(startOverlay);
  
  // Event-Listener für Start-Button
  const startButton = document.getElementById('start-session-button');
  if (startButton) {
    // Hover-Effekte
    startButton.addEventListener('mouseenter', () => {
      startButton.style.transform = 'scale(1.05) translateY(-2px)';
      startButton.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
      startButton.style.background = '#0ea471'; // Darker green on hover
    });
    
    startButton.addEventListener('mouseleave', () => {
      startButton.style.transform = 'scale(1) translateY(0)';
      startButton.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
      startButton.style.background = '#10b981'; // Back to original green
    });
    
    // Click-Handler
    startButton.addEventListener('click', () => {
      console.log(' Benutzer hat Session gestartet!');
      
      // Setze das Session-Flag
      hasStartedSession = true;
      
      // 👀 Inaktivitäts-Tracking vorbereiten (wird aktiviert sobald Produkt hinzugefügt wird)
      console.log('🎯 Session gestartet - Inaktivitäts-System bereit');
      
      // Entferne Startbildschirm mit Animation
      startOverlay.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      startOverlay.style.opacity = '0';
      startOverlay.style.transform = 'scale(0.95)';
      
      setTimeout(() => {
        startOverlay.remove();
        console.log('Startbildschirm entfernt, erstelle Kiosk-Overlay');
        createKioskOverlay();
      }, 500);
    });
  }
  
  console.log(' Startbildschirm erstellt und bereit');
}

function createKioskOverlay() {
  console.log('Erstelle Kiosk-Overlay');
  
  // Prüfe ob wir auf der Shop-Seite sind
  const shopElement = document.querySelector('app-shop');
  if (!shopElement) {
    console.log('Nicht auf Shop-Seite, warte...');
    return;
  }
  
  // Prüfe ob bereits vorhanden
  if (document.getElementById('kiosk-overlay')) {
    console.log('Kiosk-Overlay bereits vorhanden');
    return;
  }
  
  const overlay = document.createElement('div');
  overlay.id = 'kiosk-overlay';
  
  overlay.innerHTML = `
    <div id="kiosk-container">
      <!-- Tab-Navigation oben -->
      <div id="tab-navigation">
        <div id="tab-container">
          <div class="loading-message" style="font-size: 1.2rem; color: #969D9BFF; font-weight: 400;">Lade Kategorien...</div>
        </div>
      </div>

      <!-- Produktbereich -->
      <div id="product-area">
        <div id="product-grid">
          <div class="loading-message" style="font-size: 1.8rem; color: #969D9BFF; font-weight: 400;">Lade Produkte...</div>
        </div>
      </div>

      <!-- Warenkorb + Checkout unten -->
      <div id="cart-checkout-area">
        <div id="cart-summary">
          <div class="cart-empty">BITTE TICKETS AUSWÄHLEN</div>
        </div>
        <div id="button-container">
          <button id="clear-cart-button" disabled>
            <i class="fa fa-trash" style="font-size: 2.0rem;"></i>
          </button>
          <button id="checkout-button" disabled>0,00 € - Jetzt bezahlen</button>
        </div>
      </div>
    </div>
  `;
  
  document.body.appendChild(overlay);
  console.log('Kiosk-Overlay erstellt');
  
  // Event-Listener einrichten
  setupKioskEvents();

  // Start-URL speichern für spätere Verwendung
  saveStartUrl();

  // Addon-Überwachung starten
  startAddonObserver();

  // Fisheye-Effekt initialisieren
  setTimeout(() => {
    applyFisheyeEffect();
  }, 1000);

  // Einmalige optimierte Datenladung nach Angular-Bereitschaft
  setTimeout(() => {
    console.log('📊 Lade Overlay-Daten nach Angular-Bereitschaft');
    loadOriginalData();
    
    // Fallback-Check nur falls die erste Ladung fehlschlägt
    setTimeout(() => {
      const productGrid = document.getElementById('product-grid');
      const hasLoadingMessage = productGrid && productGrid.querySelector('.loading-message');
      
      if (hasLoadingMessage) {
        console.log('⚠️ Erste Datenladung unvollständig - Fallback-Versuch');
        loadOriginalData();
      } else {
        console.log('✅ Overlay-Daten erfolgreich geladen - kein Fallback nötig');
      }
    }, 2000);
  }, 1500); // Optimales Timing nach Angular-Render
  
  // Kontinuierliches Warenkorb-Monitoring starten
  startCartMonitoring();
}

function setupKioskEvents() {
  console.log('Richte Kiosk-Events ein');
  
  const checkoutButton = document.getElementById('checkout-button');
  if (checkoutButton) {
    checkoutButton.addEventListener('click', () => {
      console.log('Checkout-Button geklickt');
      triggerOriginalCheckout();
    });
  }
  
  const clearCartButton = document.getElementById('clear-cart-button');
  if (clearCartButton) {
    clearCartButton.addEventListener('click', () => {
      console.log('Warenkorb-Löschen-Button geklickt');
      triggerOriginalCartClear();
    });
  }
}

function loadOriginalData() {
  console.log('Lade Daten aus Original-Interface');

  // Tabs laden
  loadTabs();

  // Produkte laden
  loadProducts();

  // Warenkorb laden
  loadCart();
}

function loadProducts() {
  const productGrid = document.getElementById('product-grid');
  
  if (!productGrid) return;
  
  // Warte auf Original-Interface und dann auf Produktdaten
  const waitForProducts = () => {
    const originalProducts = document.querySelectorAll('app-product-listing-item');
    console.log(`Gefunden: ${originalProducts.length} Original-Produkte`);
    
    if (originalProducts.length === 0) {
      productGrid.innerHTML = '<div class="loading-message">Lade Produkte...</div>';
      
      // Warte weiter auf Produkte (bis zu 10 Sekunden)
      setTimeout(waitForProducts, 500);
      return;
    }
    
    console.log(' Produkte geladen, erstelle NEUE Kiosk-Produktkarten');
    
    // Komplett leeren und neu aufbauen
    productGrid.innerHTML = '';
    
    // Warte einen Moment damit DOM sich stabilisiert
    setTimeout(() => {
      const freshProducts = document.querySelectorAll('app-product-listing-item');
      console.log(` Erstelle ${freshProducts.length} frische Produktkarten`);
      
      freshProducts.forEach((originalProduct, index) => {
        const title = originalProduct.querySelector('.font-bold')?.textContent?.trim() || `Produkt ${index + 1}`;
        const price = originalProduct.querySelector('app-formatted-price')?.textContent?.trim() || '0,00 €';
        const isInactive = originalProduct.querySelector('.bg-red-500') !== null;
        const isAvailable = !isInactive;
        
        console.log(` Erstelle Karte ${index + 1}: ${title} - ${price} - ${isAvailable ? 'Verfügbar' : 'Nicht verfügbar'}`);
        
        const productCard = document.createElement('div');
        productCard.className = `product-card ${isAvailable ? 'available' : ''}`;
        productCard.innerHTML = `
          <div class="product-title">${title}</div>
          <div class="product-price">${price}</div>
        `;
        
        // Neue Event-Listener mit frischen Referenzen
        productCard.addEventListener('click', () => {
          console.log(`Produkt geklickt: ${title}`);
          // Hole frische Referenz zum Original-Produkt
          const currentOriginalProduct = document.querySelectorAll('app-product-listing-item')[index];
          if (currentOriginalProduct) {
            triggerOriginalProductClick(currentOriginalProduct);
          } else {
            console.log(' Original-Produkt nicht mehr gefunden');
          }
        });
        
        productGrid.appendChild(productCard);
      });
      
      // Nach dem Hinzufügen der Karten den Fisheye-Effekt anwenden
      setTimeout(() => applyFisheyeEffect(), 200);
      
    }, 100);
    
    console.log(` ${originalProducts.length} Produktkarten erstellt`);
  };
  
  // Starte sofort
  waitForProducts();
}

function loadTabs() {
  console.log('🏷️ Lade Tab-Navigation');
  const tabContainer = document.getElementById('tab-container');

  if (!tabContainer) return;

  // Warte auf Original-Interface und dann auf Tab-Daten
  const waitForTabs = () => {
    // Suche nach Tab-Navigation im Original-Interface
    const originalTabContainer = document.querySelector('.flex-grow.flex.overflow-x-scroll.hide-scrollbar');

    if (!originalTabContainer) {
      console.log('⏳ Warte auf Tab-Container...');
      setTimeout(waitForTabs, 500);
      return;
    }

    // Extrahiere alle Tab-Buttons
    const originalTabs = originalTabContainer.querySelectorAll('div[class*="m-2 p-2"]');
    console.log(`📋 Gefunden: ${originalTabs.length} Original-Tabs`);

    if (originalTabs.length === 0) {
      tabContainer.innerHTML = '<div class="loading-message">Keine Kategorien gefunden</div>';
      setTimeout(waitForTabs, 500);
      return;
    }

    // Tab-Container leeren
    tabContainer.innerHTML = '';

    // Erstelle Kiosk-Tab-Buttons
    originalTabs.forEach((originalTab, index) => {
      const tabText = originalTab.textContent?.trim() || `Kategorie ${index + 1}`;
      const isActive = originalTab.classList.contains('bg-slate-700') && originalTab.classList.contains('text-green-400');

      console.log(`📝 Erstelle Tab: ${tabText} ${isActive ? '(aktiv)' : ''}`);

      const tabButton = document.createElement('button');
      tabButton.className = `tab-button ${isActive ? 'active' : ''}`;
      tabButton.textContent = tabText;
      tabButton.dataset.tabIndex = index;

      // Event-Listener für Tab-Wechsel
      tabButton.addEventListener('click', () => {
        console.log(`🔄 Tab-Wechsel zu: ${tabText}`);
        switchToTab(index, tabText);
      });

      tabContainer.appendChild(tabButton);
    });

    console.log(`✅ ${originalTabs.length} Tab-Buttons erstellt`);

    // Setup Scroll-Funktionalität nach dem Erstellen der Tabs
    setTimeout(() => setupTabScrolling(), 100);
  };

  // Starte sofort
  waitForTabs();
}

function setupTabScrolling() {
  const tabNavigation = document.getElementById('tab-navigation');
  const tabContainer = document.getElementById('tab-container');

  if (!tabNavigation || !tabContainer) return;

  // Funktion zum Aktualisieren der Scroll-Indikatoren
  function updateScrollIndicators() {
    const canScrollLeft = tabContainer.scrollLeft > 0;
    const canScrollRight = tabContainer.scrollLeft < (tabContainer.scrollWidth - tabContainer.clientWidth);

    tabNavigation.classList.toggle('can-scroll-left', canScrollLeft);
    tabNavigation.classList.toggle('can-scroll-right', canScrollRight);
  }

  // Event-Listener für Scroll-Events
  tabContainer.addEventListener('scroll', updateScrollIndicators);

  // Initiale Aktualisierung
  updateScrollIndicators();

  // Touch-Gesten für besseres Scrolling
  let isScrolling = false;
  let startX = 0;
  let scrollLeft = 0;

  tabContainer.addEventListener('touchstart', (e) => {
    isScrolling = true;
    startX = e.touches[0].pageX - tabContainer.offsetLeft;
    scrollLeft = tabContainer.scrollLeft;
  });

  tabContainer.addEventListener('touchmove', (e) => {
    if (!isScrolling) return;
    e.preventDefault();
    const x = e.touches[0].pageX - tabContainer.offsetLeft;
    const walk = (x - startX) * 2; // Scroll-Geschwindigkeit
    tabContainer.scrollLeft = scrollLeft - walk;
  });

  tabContainer.addEventListener('touchend', () => {
    isScrolling = false;
  });

  // Maus-Drag für Desktop
  let isDragging = false;

  tabContainer.addEventListener('mousedown', (e) => {
    isDragging = true;
    startX = e.pageX - tabContainer.offsetLeft;
    scrollLeft = tabContainer.scrollLeft;
    tabContainer.style.cursor = 'grabbing';
  });

  tabContainer.addEventListener('mousemove', (e) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - tabContainer.offsetLeft;
    const walk = (x - startX) * 2;
    tabContainer.scrollLeft = scrollLeft - walk;
  });

  tabContainer.addEventListener('mouseup', () => {
    isDragging = false;
    tabContainer.style.cursor = 'grab';
  });

  tabContainer.addEventListener('mouseleave', () => {
    isDragging = false;
    tabContainer.style.cursor = 'grab';
  });

  // Cursor-Style setzen
  tabContainer.style.cursor = 'grab';

  console.log('✅ Tab-Scrolling eingerichtet');
}

function switchToTab(tabIndex, tabText) {
  console.log(`🔄 Wechsle zu Tab ${tabIndex}: ${tabText}`);

  // Finde Original-Tab-Container
  const originalTabContainer = document.querySelector('.flex-grow.flex.overflow-x-scroll.hide-scrollbar');
  if (!originalTabContainer) {
    console.log('❌ Original-Tab-Container nicht gefunden');
    return;
  }

  // Finde den entsprechenden Original-Tab
  const originalTabs = originalTabContainer.querySelectorAll('div[class*="m-2 p-2"]');
  const targetTab = originalTabs[tabIndex];

  if (!targetTab) {
    console.log(`❌ Original-Tab ${tabIndex} nicht gefunden`);
    return;
  }

  // Klicke auf den Original-Tab
  console.log(`👆 Klicke auf Original-Tab: ${tabText}`);
  targetTab.click();

  // Aktualisiere Kiosk-Tab-Buttons
  const kioskTabs = document.querySelectorAll('.tab-button');
  kioskTabs.forEach((tab, index) => {
    if (index === tabIndex) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });

  // Scrolle zum aktiven Tab
  scrollToActiveTab(tabIndex);

  // Warte kurz und lade dann neue Produkte
  setTimeout(() => {
    console.log('🔄 Lade Produkte nach Tab-Wechsel neu...');
    loadProducts();
  }, 1000);
}

function scrollToActiveTab(activeIndex) {
  const tabContainer = document.getElementById('tab-container');
  const tabButtons = document.querySelectorAll('.tab-button');

  if (!tabContainer || !tabButtons[activeIndex]) return;

  const activeTab = tabButtons[activeIndex];
  const containerWidth = tabContainer.clientWidth;
  const tabLeft = activeTab.offsetLeft;
  const tabWidth = activeTab.offsetWidth;

  // Berechne optimale Scroll-Position (Tab in der Mitte)
  const targetScrollLeft = tabLeft - (containerWidth / 2) + (tabWidth / 2);

  // Smooth scroll zum aktiven Tab
  tabContainer.scrollTo({
    left: Math.max(0, targetScrollLeft),
    behavior: 'smooth'
  });

  console.log(`📍 Scrolle zu Tab ${activeIndex}`);
}

function loadCart() {
  // Warenkorb aus Original-Interface laden
  const cartItems = document.querySelectorAll('app-cart-item-wrapper');
  const cartSummary = document.getElementById('cart-summary');
  const checkoutButton = document.getElementById('checkout-button');
  const clearCartButton = document.getElementById('clear-cart-button');
  
  if (!cartSummary || !checkoutButton || !clearCartButton) return;
  
  console.log(`Gefunden: ${cartItems.length} Warenkorb-Items`);
  
  if (cartItems.length === 0) {
    cartSummary.innerHTML = '<div class="cart-empty">BITTE TICKETS AUSWÄHLEN</div>';
    checkoutButton.disabled = true;
    checkoutButton.textContent = '0,00 €';
    clearCartButton.disabled = true;
    
    // Animation entfernen wenn der Warenkorb leer ist
    console.log('💤 Deaktiviere Pulsieren für leeren Warenkorb');
    checkoutButton.classList.remove('pulse-animation');
    return;
  }
  
  cartSummary.innerHTML = '';
  let total = 0;
  
  cartItems.forEach((item) => {
    const title = item.querySelector('.font-semibold')?.textContent?.trim() || 'Artikel';
    const priceText = item.querySelector('.text-end')?.textContent?.trim() || '0,00 €';
    const price = parseFloat(priceText.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
    total += price;
    
    // Extrahiere Anzahl aus dem weißen Bereich mit w-[10%]
    const quantityElement = item.querySelector('.w-\\[10\\%\\]');
    const quantity = quantityElement ? quantityElement.textContent?.trim() || '1' : '1';
    
    const cartItem = document.createElement('div');
    cartItem.className = 'cart-item';
    cartItem.innerHTML = `
      <span style="background: #6b7280; color: white; border-radius: 50%; width: 2.0rem; height: 2.0rem; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; font-weight: bold; flex-shrink: 0;">${quantity}</span>
      <span style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; margin-left: 0.5rem;">${title}</span>
      <span style="flex-shrink: 0; margin-left: 0.5rem;">${priceText}</span>
    `;
    cartSummary.appendChild(cartItem);
  });
  
  console.log(`📊 Warenkorb geladen: ${cartItems.length} Items, Gesamt: ${total.toFixed(2)}€`);
  
  // Scroll-Verhalten verbessern
  if (cartSummary && cartItems.length > 3) {
    cartSummary.style.setProperty('padding-right', '0.5rem', 'important');
    console.log('📜 Scrollbar aktiviert für Warenkorb mit mehr als 3 Items');
  }
  
  // Beide Buttons aktivieren/deaktivieren
  const hasItems = total > 0;
  checkoutButton.disabled = !hasItems;
  checkoutButton.textContent = `${total.toFixed(2).replace('.', ',')} € - Jetzt bezahlen`;
  clearCartButton.disabled = !hasItems;
  
  // Pulsierende Animation für den Checkout-Button hinzufügen oder entfernen
  if (hasItems) {
    console.log('💓 Aktiviere Pulsieren für Checkout-Button');
    checkoutButton.classList.add('pulse-animation');
  } else {
    checkoutButton.classList.remove('pulse-animation');
  }
}

function applyFisheyeEffect() {
  console.log('🔍 Initialisiere Fisheye-Effekt für Produktkarten');
  const productArea = document.getElementById('product-area');
  const productCards = document.querySelectorAll('.product-card');
  
  if (!productArea || productCards.length === 0) {
    console.log('❌ Produktbereich oder -karten nicht gefunden, kann Fisheye-Effekt nicht anwenden');
    return;
  }
  
  console.log(`✅ Fisheye-Effekt wird auf ${productCards.length} Karten angewendet`);
  
  // Initiale Anwendung des Effekts
  updateFisheyeEffect();
  
  // Event-Listener für Scroll-Ereignis
  productArea.removeEventListener('scroll', updateFisheyeEffect); // Entferne vorherige, falls vorhanden
  productArea.addEventListener('scroll', updateFisheyeEffect);
  
  function updateFisheyeEffect() {
    // Deaktivierter Fisheye-Effekt - Alle Karten haben gleiche Größe und Transparenz
    console.log('🔍 Fisheye-Effekt deaktiviert - Wende einheitliches Styling an');
    
    // Für alle Karten die gleichen Werte anwenden
    productCards.forEach((card, index) => {
      // Konstante Werte ohne Verzerrung
      const scale = 1.0;      // Keine Skalierung
      const opacity = 1.0;    // Volle Deckkraft
      const zIndex = 10;      // Standard Z-Index
      const translateY = 0;   // Keine Verschiebung
      
      // Wende einheitliche Transformationen ohne Fisheye-Effekt an
      card.style.transform = `scale(${scale}) translateY(${translateY}px)`;
      card.style.opacity = opacity;
      card.style.zIndex = zIndex;
      card.style.transformOrigin = 'center';
      
      // Einheitlicher Abstand zwischen den Karten
      if (index < productCards.length - 1) {
        card.style.marginBottom = '16px';
      }
    });
  }
}

function createLoadingOverlay() {
  let overlay = document.getElementById('kiosk-loading-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'kiosk-loading-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(34, 40, 49, 0.8); /* Transparent anthracite */
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99999; /* Ensure it's on top */
      pointer-events: all; /* Block all input */
    `;

    // Container für Spinner und Text
    const container = document.createElement('div');
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    `;
    
    const spinner = document.createElement('div');
    spinner.className = 'kiosk-spinner';
    spinner.style.cssText = `
      border: 8px solid #f3f3f3; /* Light grey */
      border-top: 8px solid #10b981; 
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 2s linear infinite;
    `;
    
    // Text-Element für Status
    const text = document.createElement('div');
    text.className = 'loading-text';
    text.textContent = 'Wird verarbeitet...';
    text.style.cssText = `
      color: white;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    container.appendChild(spinner);
    container.appendChild(text);
    overlay.appendChild(container);
    document.body.appendChild(overlay);

    // Add keyframes for spinner animation
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(styleSheet);
  }
  overlay.style.display = 'flex';
  loadingOverlay = overlay; // Setze globale Referenz
}

function removeLoadingOverlay() {
  const overlay = document.getElementById('kiosk-loading-overlay');
  if (overlay) {
    overlay.style.display = 'none';
  }
}

// Spezielle Ladeanimation für Gutscheinverarbeitung
function createVoucherLoadingOverlay() {
  let overlay = document.getElementById('voucher-loading-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'voucher-loading-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(34, 40, 49, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99999;
      pointer-events: all;
    `;

    // Container für Spinner und Text
    const container = document.createElement('div');
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    `;

    const spinner = document.createElement('div');
    spinner.className = 'voucher-spinner';
    spinner.style.cssText = `
      border: 8px solid #f3f3f3;
      border-top: 8px solid #059669;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 2s linear infinite;
    `;

    // Text-Element für Status
    const text = document.createElement('div');
    text.className = 'voucher-loading-text';
    text.textContent = 'Gutschein wird eingelöst...';
    text.style.cssText = `
      color: white;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    container.appendChild(spinner);
    container.appendChild(text);
    overlay.appendChild(container);
    document.body.appendChild(overlay);
  }
  overlay.style.display = 'flex';
  return overlay;
}

function removeVoucherLoadingOverlay() {
  const overlay = document.getElementById('voucher-loading-overlay');
  if (overlay) {
    overlay.style.display = 'none';
  }
}

function triggerOriginalProductClick(originalProduct) {
  // Show loading overlay immediately
  createLoadingOverlay();
  
  // Original-Interface temporär einblenden für Event-Handling
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    appRoot.style.setProperty('display', 'block', 'important');
    console.log('Original-Interface temporär eingeblendet');
  }
  
  // Prüfe ob wir gerade vom Startscreen kommen und möglicherweise ein Reload brauchen
  if (!hasStartedSession) {
    console.log('🔄 Erster Produktklick nach Startscreen - verwende erweiterte Strategie');
    
    // Erweitere Loading-Overlay für schöneren Reload-Übergang
    if (loadingOverlay) {
      const overlayText = loadingOverlay.querySelector('.loading-text');
      if (overlayText) {
        overlayText.textContent = 'Bereite Warenkorb vor...';
      }
    }
    
    // Force-Reload der Angular-Komponenten durch Seiten-Refresh für frische Cart ID
    setTimeout(() => {
      console.log('🔄 Führe Seiten-Reload für frische Session durch...');
      window.location.reload();
      // Loading-Overlay wird durch Reload automatisch entfernt
    }, 800); // Etwas längere Wartezeit für besseren visuellen Übergang
    return;
  }
  
  // Normale Produkt-Click-Behandlung
  const clickableElement = originalProduct.querySelector('[presshandler]') || originalProduct;
  console.log('Clickbares Element:', clickableElement);
  
  if (clickableElement) {
    // Position des Elements ermitteln für realistische Klicks
    const rect = clickableElement.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    console.log(`Klicke auf Position: ${centerX}, ${centerY}`);
    
    // Erweiterte Click-Strategie für bessere Zuverlässigkeit
    const performProductClick = () => {
      // Methode 1: Direkter Click
      try {
        clickableElement.click();
        console.log('✅ Direkter Produktklick ausgeführt');
      } catch (e) {
        console.log('❌ Direkter Produktklick fehlgeschlagen:', e.message);
      }
      
      // Methode 2: Event-Sequence
      const events = [
        new MouseEvent('mousedown', {
          bubbles: true,
          cancelable: true,
          view: window,
          clientX: centerX,
          clientY: centerY,
          button: 0
        }),
        new MouseEvent('mouseup', {
          bubbles: true,
          cancelable: true,
          view: window,
          clientX: centerX,
          clientY: centerY,
          button: 0
        }),
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
          clientX: centerX,
          clientY: centerY,
          button: 0
        })
      ];
      
      // Events mit kleinen Delays abfeuern
      events.forEach((event, index) => {
        setTimeout(() => {
          try {
            console.log(`Feuere ${event.type} Event`);
            clickableElement.dispatchEvent(event);
          } catch (e) {
            console.log(`❌ ${event.type} Event fehlgeschlagen:`, e.message);
          }
        }, (index + 1) * 50); // Start nach direktem Click
      });
      
      // Methode 3: Focus + Enter als Additional Fallback
      setTimeout(() => {
        try {
          if (clickableElement.focus) {
            clickableElement.focus();
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              bubbles: true
            });
            clickableElement.dispatchEvent(enterEvent);
            console.log('✅ Focus + Enter Event ausgeführt');
          }
        } catch (e) {
          console.log('❌ Focus + Enter fehlgeschlagen:', e.message);
        }
      }, 250);
    };
    
    performProductClick();
  }
  
  // Erweiterte Warenkorb-Überwachung nach Produktklick
  const monitorCartUpdate = () => {
    let attempts = 0;
    const maxAttempts = 10; // 5 Sekunden überwachen
    const initialCartCount = document.querySelectorAll('app-cart-item-wrapper').length;
    
    const checkCartUpdate = () => {
      attempts++;
      const currentCartCount = document.querySelectorAll('app-cart-item-wrapper').length;
      
      if (currentCartCount > initialCartCount) {
        console.log('✅ Warenkorb-Update erkannt nach Produktklick');
        loadCart();
        
        // 👀 Inaktivitäts-Timer starten/zurücksetzen nach erfolgreichem Produktklick
        resetInactivityTimer();
        console.log('⏰ Inaktivitäts-Timer gestartet nach Produktzugabe');
        
        return true; // Success
      }
      
      if (attempts < maxAttempts) {
        setTimeout(checkCartUpdate, 500);
        return false;
      } else {
        console.log('⚠️ Kein Warenkorb-Update nach Produktklick erkannt - erzwinge Reload');
        loadCart();
        // Optional: Retry-Mechanismus oder Benutzer-Feedback
        return false;
      }
    };
    
    setTimeout(checkCartUpdate, 500); // Start monitoring nach 500ms
  };
  
  // Original-Interface nach angemessener Zeit wieder verstecken
  setTimeout(() => {
    if (appRoot) {
      appRoot.style.setProperty('display', 'none', 'important');
      console.log('Original-Interface wieder versteckt');
    }
    console.log('Lade Warenkorb neu nach Produktklick');
    loadCart();
    monitorCartUpdate(); // Starte Überwachung
    removeLoadingOverlay();
  }, 2500); // Längere Wartezeit für bessere API-Verarbeitung
}

function triggerOriginalCartClear() {
  createLoadingOverlay();
  console.log('🗑️ Triggere Original-Warenkorb-Leerung');
  
  // Setze Flag für Cart-Clear-Erkennung
  cartWasJustCleared = true;
  console.log('🚩 Cart-Clear-Flag gesetzt');
  
  // Sicherheit: Flag nach 10 Sekunden zurücksetzen falls nicht verwendet
  setTimeout(() => {
    if (cartWasJustCleared) {
      cartWasJustCleared = false;
      console.log('🕒 Cart-Clear-Flag automatisch zurückgesetzt (Timeout)');
    }
  }, 10000);
  
  // Original-Interface temporär einblenden für Button-Suche
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    appRoot.style.setProperty('display', 'block', 'important');
    console.log('Original-Interface für Warenkorb-Leerung eingeblendet');
  }
  
  // Warte kurz, dann suche Original-Trash-Button
  setTimeout(() => {
    console.log('🔍 Suche nach Original-Trash-Button...');
    
    // Erweiterte Suche nach Trash-Button mit mehreren Selektoren
    let originalTrashButton = null;
    
    // Verschiedene Selektoren für Trash-Button probieren
    const trashSelectors = [
      'app-touch-button button i.fal.fa-trash',
      'app-touch-button button i.fa-trash',
      'app-touch-button button i.fas.fa-trash',
      'button i.fal.fa-trash',
      'button i.fa-trash',
      'button i.fas.fa-trash',
      'i.fal.fa-trash',
      'i.fa-trash',
      'i.fas.fa-trash'
    ];
    
    for (const selector of trashSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        originalTrashButton = element.closest('button') || element;
        console.log(`✅ Trash-Button gefunden mit Selektor: ${selector}`);
        break;
      }
    }
    
    // Fallback: Suche nach allen Buttons mit Trash-Icon
    if (!originalTrashButton) {
      console.log('🔍 Fallback: Suche alle Buttons mit Trash-Icon...');
      const allButtons = document.querySelectorAll('button');
      for (const button of allButtons) {
        const icon = button.querySelector('i[class*="trash"], i[class*="fa-trash"]');
        if (icon) {
          originalTrashButton = button;
          console.log('✅ Trash-Button via Fallback gefunden');
          break;
        }
      }
    }
    
    if (originalTrashButton) {
      console.log('🖱️ Klicke auf Trash-Button');
      
      // Mehrfach-Click-Strategie für bessere Zuverlässigkeit
      const performClick = () => {
        try {
          originalTrashButton.click();
          console.log('✅ Direkter Click ausgeführt');
        } catch (e) {
          console.log('❌ Direkter Click fehlgeschlagen:', e.message);
        }
        
        // Event-basierter Click als Backup
        setTimeout(() => {
          try {
            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window,
              button: 0
            });
            originalTrashButton.dispatchEvent(clickEvent);
            console.log('✅ Event-Click ausgeführt');
          } catch (e) {
            console.log('❌ Event-Click fehlgeschlagen:', e.message);
          }
        }, 100);
      };
      
      performClick();
      
      // Erweiterte Suche nach Bestätigen-Button mit Retry-Logik
      const searchForConfirmButton = (attempt = 1, maxAttempts = 5) => {
        console.log(`🔍 Suche nach Bestätigen-Button (Versuch ${attempt}/${maxAttempts})...`);
        
        const confirmSelectors = [
          'button.wiz-accept-button',
          'button[class*="accept"]',
          'button[class*="confirm"]',
          '.modal button:last-child',
          '.dialog button:last-child'
        ];
        
        let confirmButton = null;
        
        // Verschiedene Selektoren probieren
        for (const selector of confirmSelectors) {
          const element = document.querySelector(selector);
          if (element && element.offsetParent !== null) { // Element ist sichtbar
            confirmButton = element;
            console.log(`✅ Bestätigen-Button gefunden mit: ${selector}`);
            break;
          }
        }
        
        // Fallback: Suche nach Button mit bestimmtem Text
        if (!confirmButton) {
          const allButtons = document.querySelectorAll('button');
          for (const button of allButtons) {
            if (button.offsetParent !== null) { // Button ist sichtbar
              const text = button.textContent?.toLowerCase().trim() || '';
              if (text.includes('ja') || text.includes('löschen') || text.includes('bestätigen') || text.includes('ok')) {
                confirmButton = button;
                console.log(`✅ Bestätigen-Button via Text gefunden: "${button.textContent}"`);
                break;
              }
            }
          }
        }
        
        if (confirmButton) {
          console.log('🖱️ Klicke auf Bestätigen-Button');
          
          try {
            confirmButton.click();
            console.log('✅ Bestätigung Click ausgeführt');
          } catch (e) {
            console.log('❌ Bestätigung Click fehlgeschlagen:', e.message);
          }
          
          // Event-Click als Backup
          setTimeout(() => {
            try {
              const confirmClickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
              });
              confirmButton.dispatchEvent(confirmClickEvent);
              console.log('✅ Bestätigung Event-Click ausgeführt');
            } catch (e) {
              console.log('❌ Bestätigung Event-Click fehlgeschlagen:', e.message);
            }
          }, 50);
        } else if (attempt < maxAttempts) {
          // Retry nach kurzer Verzögerung
          setTimeout(() => searchForConfirmButton(attempt + 1, maxAttempts), 300);
        } else {
          console.log('❌ Bestätigen-Button nach allen Versuchen nicht gefunden');
        }
      };
      
      // Starte Suche nach Bestätigen-Button nach kurzer Verzögerung
      setTimeout(() => searchForConfirmButton(), 300);
      
    } else {
      console.log('❌ Kein Trash-Button gefunden!');
      // Direkte Suche nach Bestätigen-Dialog falls Trash-Button übersprungen wurde
      setTimeout(() => {
        const confirmButton = document.querySelector('button.wiz-accept-button');
        if (confirmButton) {
          console.log('✅ Bestätigen-Dialog ohne Trash-Click gefunden');
          confirmButton.click();
        }
      }, 300);
    }
    
    // Interface wieder verstecken und direkt zum Startscreen wechseln
    setTimeout(() => {
      if (appRoot) {
        appRoot.style.setProperty('display', 'none', 'important');
        console.log('Original-Interface wieder versteckt');
      }
      
      // Warte auf API-Calls, dann direkt zum Startscreen
      setTimeout(() => {
        console.log('✅ Warenkorb geleert - wechsle direkt zum Startscreen');
        removeLoadingOverlay();
        
        // Session zurücksetzen für neuen Start
        hasStartedSession = false;
        console.log('🔄 Session zurückgesetzt für neuen Start');
        
        // Entferne das alte Kiosk-Overlay falls vorhanden
        const existingKioskOverlay = document.getElementById('kiosk-overlay');
        if (existingKioskOverlay) {
          existingKioskOverlay.remove();
          console.log('🗑️ Altes Kiosk-Overlay entfernt');
        }
        
        // Zeige direkt den Startscreen
        createStartScreen();
        console.log('🎯 Startscreen nach Warenkorb-Löschung angezeigt');
        
        // Flag zurücksetzen da wir direkt gewechselt haben
        cartWasJustCleared = false;
      }, 800);
    }, 1500); // Längere Wartezeit für API-Calls
    
  }, 500); // Längere initiale Wartezeit
}

function triggerOriginalCheckout() {
  console.log('Triggere Original-Checkout');

  // SOFORT Checkout-Overlay erstellen um Flackern zu vermeiden
  console.log('🎨 Erstelle Checkout-Overlay sofort beim Klick');
  createCheckoutOverlay();

  // Original-Interface sofort verstecken
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    appRoot.style.setProperty('display', 'none', 'important');
    console.log('Original-Interface sofort versteckt');
  }

  // Kiosk-Overlay verstecken
  const kioskOverlay = document.getElementById('kiosk-overlay');
  if (kioskOverlay) {
    kioskOverlay.style.setProperty('display', 'none', 'important');
    console.log('Kiosk-Overlay versteckt');
  }

  // Erweiterte Suche nach Checkout-Button
  console.log('🔍 Suche nach Original-Checkout-Button...');

  // Verschiedene Selektoren für Checkout-Button
  const checkoutSelectors = [
    'app-checkout-button button',
    'app-checkout-button',
    '[class*="checkout"] button',
    'button[class*="checkout"]',
    'button:contains("Checkout")',
    'button:contains("Zur Kasse")',
    'button:contains("Kasse")'
  ];

  let originalCheckoutBtn = null;

  for (const selector of checkoutSelectors) {
    try {
      originalCheckoutBtn = document.querySelector(selector);
      if (originalCheckoutBtn) {
        console.log(`✅ Checkout-Button gefunden mit Selektor: ${selector}`);
        break;
      }
    } catch (e) {
      console.log(`Selektor ${selector} fehlgeschlagen:`, e.message);
    }
  }

  // Fallback: Suche nach Button mit Checkout-Text
  if (!originalCheckoutBtn) {
    console.log('🔍 Fallback: Suche nach Button mit Checkout-Text...');
    const allButtons = document.querySelectorAll('button');
    for (const button of allButtons) {
      const text = button.textContent?.toLowerCase() || '';
      if (text.includes('checkout') || text.includes('kasse') || text.includes('zur kasse')) {
        originalCheckoutBtn = button;
        console.log(`✅ Checkout-Button gefunden via Text: "${button.textContent}"`);
        break;
      }
    }
  }

  if (originalCheckoutBtn) {
    console.log('Original-Checkout-Button gefunden, analysiere...');
    console.log('Button Text:', originalCheckoutBtn.textContent);
    console.log('Button Classes:', originalCheckoutBtn.className);
    console.log('Button Parent:', originalCheckoutBtn.parentElement?.tagName);

    // Prüfe ob Button aktiviert ist
    if (originalCheckoutBtn.disabled) {
      console.log('⚠️ Checkout-Button ist deaktiviert!');
      // Versuche trotzdem zu klicken
    }

    // Mehrere Click-Methoden versuchen
    console.log('🖱️ Versuche verschiedene Click-Methoden...');

    // Methode 1: Direkter Click
    try {
      originalCheckoutBtn.click();
      console.log('✅ Direkter Click ausgeführt');
    } catch (e) {
      console.log('❌ Direkter Click fehlgeschlagen:', e.message);
    }

    // Methode 2: Event-basierter Click
    setTimeout(() => {
      try {
        const rect = originalCheckoutBtn.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
          clientX: centerX,
          clientY: centerY,
          button: 0
        });

        originalCheckoutBtn.dispatchEvent(clickEvent);
        console.log('✅ Event-basierter Click ausgeführt');
      } catch (e) {
        console.log('❌ Event-basierter Click fehlgeschlagen:', e.message);
      }
    }, 100);

    // Methode 3: Focus + Enter (falls nötig)
    setTimeout(() => {
      try {
        originalCheckoutBtn.focus();
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          bubbles: true
        });
        originalCheckoutBtn.dispatchEvent(enterEvent);
        console.log('✅ Focus + Enter ausgeführt');
      } catch (e) {
        console.log('❌ Focus + Enter fehlgeschlagen:', e.message);
      }
    }, 200);

    // Checkout-Overlay wurde bereits erstellt, keine weitere Erkennung nötig
    console.log('✅ Checkout-Prozess gestartet mit sofortigem Overlay');

  } else {
    console.log('❌ Original-Checkout-Button nicht gefunden');
    // Zurück zum Kiosk-Interface
    setTimeout(() => {
      if (appRoot) {
        appRoot.style.setProperty('display', 'none', 'important');
      }
      if (kioskOverlay) {
        kioskOverlay.style.setProperty('display', 'block', 'important');
      }
    }, 1000);
  }
}

function startImprovedCheckoutDetection(appRoot, kioskOverlay) {
  console.log('🔍 Starte verbesserte Checkout-Erkennung');

  let checkAttempts = 0;
  const maxAttempts = 40; // 20 Sekunden warten (500ms * 40)

  const checkForCheckout = () => {
    checkAttempts++;
    console.log(`\n=== Checkout-Check ${checkAttempts}/${maxAttempts} ===`);

    // Mehrere Erkennungsmethoden verwenden
    const isCheckoutDetected = detectCheckoutPage();

    if (isCheckoutDetected.detected) {
      console.log(`✅ Checkout-Seite erfolgreich erkannt! (Methode: ${isCheckoutDetected.method})`);
      console.log('Interface bleibt sichtbar für Checkout-Prozess');

      // Zusätzliche Bestätigung nach 2 Sekunden
      setTimeout(() => {
        const doubleCheck = detectCheckoutPage();
        if (doubleCheck.detected) {
          console.log('✅ Checkout-Erkennung bestätigt - alles OK');
        } else {
          console.log('⚠️ Checkout-Erkennung nicht bestätigt, aber bleibe trotzdem');
        }
      }, 2000);

      return;
    }

    if (checkAttempts < maxAttempts) {
      console.log(`❌ Checkout noch nicht erkannt, warte weitere 500ms...`);
      setTimeout(checkForCheckout, 500);
    } else {
      console.log('❌ Checkout-Navigation fehlgeschlagen nach 20 Sekunden');
      console.log('Führe finale Diagnose durch...');

      // Finale Diagnose
      console.log('=== FINALE DIAGNOSE ===');
      console.log('URL:', window.location.href);
      console.log('Alle app-* Elemente:', Array.from(document.querySelectorAll('[class*="app-"]')).map(el => el.tagName));
      console.log('Alle Komponenten:', Array.from(document.querySelectorAll('*')).filter(el => el.tagName.startsWith('APP-')).map(el => el.tagName));
      console.log('Body innerHTML length:', document.body.innerHTML.length);

      // Zurück zum Kiosk-Interface
      console.log('Kehre zum Kiosk-Overlay zurück...');
      if (appRoot) {
        appRoot.style.setProperty('display', 'none', 'important');
      }
      if (kioskOverlay) {
        kioskOverlay.style.setProperty('display', 'block', 'important');
      }
    }
  };

  // Check nach 3 Sekunden starten (noch mehr Zeit für Angular)
  console.log('Warte 3 Sekunden bevor Checkout-Erkennung startet...');
  setTimeout(checkForCheckout, 3000);
}

function detectCheckoutPage() {
  console.log('🔍 Detaillierte Checkout-Erkennung gestartet...');

  // Debug: Aktuelle DOM-Struktur loggen
  console.log('Aktuelle URL:', window.location.href);
  console.log('Aktuelle Hash:', window.location.hash);
  console.log('Body classes:', document.body.className);
  console.log('App-root innerHTML length:', document.querySelector('app-root')?.innerHTML?.length || 0);

  // Methode 1: Standard app-checkout Element
  const checkoutElement = document.querySelector('app-checkout');
  console.log('app-checkout Element gefunden:', !!checkoutElement);
  if (checkoutElement) {
    console.log('✅ Checkout erkannt via app-checkout Element');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'app-checkout-element' };
  }

  // Methode 2: Payment-Options (sehr zuverlässig)
  const paymentOptions = document.querySelector('app-payment-options');
  console.log('app-payment-options Element gefunden:', !!paymentOptions);
  if (paymentOptions) {
    console.log('✅ Checkout erkannt via payment-options');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'payment-options' };
  }

  // Methode 3: Checkout-spezifische Komponenten (einzeln prüfen)
  const checkoutComponents = [
    'app-payment-customer-form',
    'app-checkout-details',
    'app-payment-voucher-form',
    'app-numeric-keyboard'
  ];

  for (const component of checkoutComponents) {
    const element = document.querySelector(component);
    console.log(`${component} gefunden:`, !!element);
    if (element) {
      console.log(`✅ Checkout erkannt via ${component}`);
      applyCheckoutKioskDesign();
      return { detected: true, method: `component-${component}` };
    }
  }

  // Methode 4: Checkout-Header Text
  const headerElements = document.querySelectorAll('header, h1, h2, .header, [class*="header"]');
  for (const header of headerElements) {
    if (header.textContent && header.textContent.trim().toLowerCase().includes('checkout')) {
      console.log('✅ Checkout erkannt via Header-Text:', header.textContent.trim());
      applyCheckoutKioskDesign();
      return { detected: true, method: 'header-text' };
    }
  }

  // Methode 5: Fortschrittsbalken (spezifisch für Checkout)
  const progressBar = document.querySelector('[style*="width: 92.1111%"]');
  console.log('Fortschrittsbalken gefunden:', !!progressBar);
  if (progressBar) {
    console.log('✅ Checkout erkannt via Fortschrittsbalken');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'progress-bar' };
  }

  // Methode 6: Kunde-Formular (sehr spezifisch für Checkout)
  const kundeText = Array.from(document.querySelectorAll('*')).find(el =>
    el.textContent && el.textContent.trim() === 'Kunde' && el.tagName === 'H2'
  );
  console.log('Kunde H2 Element gefunden:', !!kundeText);
  if (kundeText) {
    console.log('✅ Checkout erkannt via Kunde-Formular');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'kunde-form' };
  }

  // Methode 7: Positionssummen (nur auf Checkout)
  const positionssummen = Array.from(document.querySelectorAll('*')).find(el =>
    el.textContent && el.textContent.includes('Positionssummen')
  );
  console.log('Positionssummen Element gefunden:', !!positionssummen);
  if (positionssummen) {
    console.log('✅ Checkout erkannt via Positionssummen');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'positionssummen' };
  }

  // Methode 8: URL-basierte Erkennung
  if (window.location.href.includes('checkout') || window.location.hash.includes('checkout')) {
    console.log('✅ Checkout erkannt via URL');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'url-based' };
  }

  // Methode 9: Addon-Seite erkennen und automatisch überspringen
  const addonWizard = document.querySelector('app-product-wizard');
  const addonComponent = document.querySelector('app-addons');
  console.log('app-product-wizard gefunden:', !!addonWizard);
  console.log('app-addons gefunden:', !!addonComponent);

  if (addonWizard || addonComponent || window.location.href.includes('addons')) {
    console.log('🚀 Addon-Seite erkannt - überspringe automatisch ohne Anzeige');
    skipAddonsAutomatically();
    // Gib false zurück, damit weiter nach Checkout gesucht wird
    return { detected: false, method: 'addon-auto-skip' };
  }

  // Methode 10: Andere Wizard-Seiten überspringen
  if (window.location.href.includes('wizard')) {
    console.log('⚠️ Wizard-Seite erkannt - versuche zu überspringen');
    skipWizardIfPossible();
    // Gib false zurück, damit weiter gesucht wird
    return { detected: false, method: 'wizard-skip' };
  }

  // Methode 9: Fallback - prüfe ob wir nicht mehr auf Shop-Seite sind
  const shopElement = document.querySelector('app-shop');
  const hasShoppingCart = document.querySelector('app-shopping-cart');
  const hasPaymentElements = document.querySelector('[class*="payment"]') ||
                            document.querySelector('[class*="Payment"]');

  console.log('Shop Element gefunden:', !!shopElement);
  console.log('Shopping Cart gefunden:', !!hasShoppingCart);
  console.log('Payment Elemente gefunden:', !!hasPaymentElements);

  if (!shopElement && hasShoppingCart && hasPaymentElements) {
    console.log('✅ Checkout erkannt via Fallback-Methode (kein Shop + Cart + Payment)');
    applyCheckoutKioskDesign();
    return { detected: true, method: 'fallback-detection' };
  }

  console.log('❌ Checkout nicht erkannt - alle Methoden fehlgeschlagen');
  return { detected: false, method: 'none' };
}

function applyCheckoutKioskDesign() {
  console.log('🎨 Wende Checkout-Kiosk-Design an');

  // Prüfe ob bereits angewendet
  if (document.getElementById('checkout-kiosk-applied')) {
    return;
  }

  // SOFORT Original-Interface verstecken um Flackern zu vermeiden
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    appRoot.style.setProperty('display', 'none', 'important');
    console.log('Original-Interface sofort versteckt');
  }

  // Markierung setzen
  const marker = document.createElement('div');
  marker.id = 'checkout-kiosk-applied';
  marker.style.display = 'none';
  document.body.appendChild(marker);

  // Erstelle Checkout-Overlay im Kiosk-Stil
  createCheckoutOverlay();
}

function createCheckoutOverlay() {
  console.log('🎨 Erstelle Checkout-Overlay im Kiosk-Stil');

  // Prüfe ob bereits vorhanden
  if (document.getElementById('checkout-overlay')) {
    console.log('Checkout-Overlay bereits vorhanden');
    updateCheckoutOverlay(); // Aktualisiere das bestehende Overlay
    return;
  }

  // Erstelle Checkout-Overlay
  const checkoutOverlay = document.createElement('div');
  checkoutOverlay.id = 'checkout-overlay';
  checkoutOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #f3f4f6;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  // Hole Warenkorb-Daten und Gutscheine
  const cartItems = getCartItems();
  const vouchers = getAppliedVouchers();
  const subtotal = calculateTotal(cartItems);
  const voucherDiscount = calculateVoucherDiscount(vouchers);
  const finalTotal = subtotal - voucherDiscount;

  checkoutOverlay.innerHTML = `
    <div style="display: flex; flex-direction: column; height: 100vh;">

      <!-- Main Content -->
      <div style="flex: 1; display: flex; flex-direction: column; padding: 0; gap: 0; height: 100vh; overflow: hidden;">

        <!-- Top: Checkout Details & Voucher (50% der verfügbaren Höhe) -->
        <div style="flex: 1; background: linear-gradient(135deg, #485566FF 0%, #334155 100%); border-radius: 0; padding: 1.5rem; box-shadow: none; display: flex; flex-direction: column; overflow: hidden; color: white;">

          <!-- Positionssummen -->
          <div style="margin-bottom: 1.5rem; padding-top: 10px;">
            <div style="border-bottom: 2px solid rgba(255,255,255,0.3); padding-bottom: 0.8rem; margin-bottom: 0.8rem; display: flex; justify-content: space-between;">
              <p style="font-size: 1.4rem; font-weight: 700; margin: 0; color: white;">Positionssummen</p>
              <div style="display: flex; justify-content: space-between; width: 50%;">
                <p style="font-style: italic; margin: 0; font-size: 1.1rem; color: rgba(255,255,255,0.8);">Netto</p>
                <p class="netto-amount" style="font-size: 1.4rem; font-weight: 700; margin: 0; color: white;">${subtotal.toFixed(2).replace('.', ',')} €</p>
              </div>
            </div>

            <div style="border-bottom: 1px solid rgba(255,255,255,0.2); padding: 0.5rem 0; display: flex; justify-content: space-between;">
              <p style="font-size: 1.1rem; margin: 0; color: rgba(255,255,255,0.8);">+ MwSt. 0%</p>
              <p style="font-size: 1.1rem; margin: 0; color: rgba(255,255,255,0.8);">0,00 €</p>
            </div>

            <div style="border-bottom: 2px solid rgba(255,255,255,0.3); padding: 0.5rem 0; margin-top: 0.5rem; display: flex; justify-content: space-between;">
              <p style="font-size: 1.4rem; font-weight: 700; margin: 0; color: white;">Summe</p>
              <div style="display: flex; justify-content: space-between; width: 50%;">
                <p style="font-style: italic; margin: 0; font-size: 1.1rem; color: rgba(255,255,255,0.8);">Brutto</p>
                <p class="brutto-amount" style="font-size: 1.4rem; font-weight: 700; margin: 0; color: white;">${subtotal.toFixed(2).replace('.', ',')} €</p>
              </div>
            </div>
          </div>

          <!-- Angewendete Gutscheine -->
          <div id="voucher-container" style="margin-bottom: 1.5rem; ${vouchers.length === 0 ? 'display: none;' : ''}">
            ${vouchers.map(voucher => `
              <div style="display: flex; justify-content: space-between; padding: 0.8rem 0; border-bottom: 1px solid rgba(255,255,255,0.2); background: rgba(255,255,255,0.1); border-radius: 8px; margin-bottom: 0.5rem; padding-left: 1rem; padding-right: 1rem;">
                <p style="font-size: 1.2rem; margin: 0; color: white; font-weight: 600;">${voucher.name}</p>
                <p style="font-size: 1.2rem; margin: 0; color: #ff6b6b; font-weight: 700;">${voucher.amount}</p>
              </div>
            `).join('')}
          </div>

          <!-- Gutschein/Rabattcode Eingabe -->
          <div style="margin-bottom: 1.5rem;">
            <div style="display: flex; gap: 0.8rem;">
              <input type="text" id="voucher-input" style="flex: 1; font-size: 1.2rem; padding: 1rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 12px; min-height: 60px; background: rgba(255,255,255,0.1); color: white; backdrop-filter: blur(10px);" placeholder="Gutschein- oder Rabattcode eingeben">
              <button id="voucher-add-button" style="background: rgba(255,255,255,0.2); padding: 1rem; border-radius: 12px; border: 2px solid rgba(255,255,255,0.3); min-width: 60px; cursor: pointer; opacity: 0.5; color: white; backdrop-filter: blur(10px);" disabled>
                <i style="font-size: 1.4rem; font-weight: bold;">+</i>
              </button>
            </div>
          </div>

          <!-- Gesamtsumme - Viel prominenter -->
          <div style="border: 3px solid #10b981; border-radius: 16px; padding: 1.5rem; display: flex; justify-content: space-between; background: linear-gradient(135deg, #10b981 0%, #059669 100%); margin-top: auto; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);">
            <p style="font-size: 2rem; font-weight: 900; margin: 0; color: white; text-shadow: 0 0px 0px rgba(0,0,0,0.3);">Gesamtsumme</p>
            <p class="final-total" style="font-size: 2.2rem; font-weight: 900; margin: 0; color: white; text-shadow: 0 0px 0px rgba(0,0,0,0.3);">${finalTotal.toFixed(2).replace('.', ',')} €</p>
          </div>
        </div>

        <!-- Bottom: Cart Summary (50% der verfügbaren Höhe) -->
        <div style="flex: 1; background: #374151; color: white; border-radius: 0; padding: 1rem; display: flex; flex-direction: column; overflow: hidden; position: relative;">
          

          <div id="checkout-cart-scroll" style="flex: 1; overflow-y: auto; margin-bottom: 0.8rem; min-height: 0; padding-right: 1rem;">
            ${cartItems.map(item => `
              <div class="cart-item" style="display: flex; justify-content: space-between; align-items: center; font-size: 2.0rem; padding: 0.5rem 0; border-bottom: 1px solid #4b5563; flex-shrink: 0; min-height: 40px;">
                <div style="display: flex; align-items: center;">
                  <span style="background: #6b7280; color: white; border-radius: 50%; width: 2.0rem; height: 2.0rem; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; font-weight: bold; flex-shrink: 0;">${item.quantity}</span>
                  <span style="flex: 1; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; margin-left: 0.5rem;">${item.name}</span>
                </div>
                <span style="flex-shrink: 0; margin-left: 0.5rem;">${item.price}</span>
              </div>
            `).join('')}
          </div>

          <div style="position: absolute; bottom: 0; left: 0; right: 0; display: flex; height: 100px; gap: 0;">
            <button id="checkout-clear-cart-button" style="flex: 1; background: #dc2626; color: white; border: none; border-radius: 0; font-size: 1.2rem; font-weight: bold; cursor: pointer; transition: background 0.15s ease; height: 100px;">
              <i class="fa fa-trash" style="font-size: 2.0rem;"></i>
            </button>
            <button id="checkout-pay-button" style="flex: 3; background: #059669; color: white; border: none; border-radius: 0; font-size: 2.6rem; font-weight: 600; cursor: pointer; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); height: 100px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden;" class="pulse-enhanced">
              Mit EC-Karte bezahlen
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(checkoutOverlay);

  // Zusätzliche CSS-Styles für bessere Darstellung
  
  // Button-Animation direkt anwenden
  const payButton = document.getElementById('checkout-pay-button');
  if (payButton) {
    // Click-Effekt
    payButton.addEventListener('click', (event) => {
      triggerButtonMorphing(payButton, event, 'bounce');
    });
    
    // Hover-Effekte wie beim Start-Button
    payButton.addEventListener('mouseenter', () => {
      payButton.style.transform = 'scale(1.05) translateY(-2px)';
      payButton.style.boxShadow = '0 6px 8px rgba(0, 0, 0, 0.15)';
      payButton.style.background = '#0ea471'; // Dunkleres Grün beim Hover
    });
    
    payButton.addEventListener('mouseleave', () => {
      payButton.style.transform = 'scale(1) translateY(0)';
      payButton.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
      payButton.style.background = '#059669'; // Zurück zur ursprünglichen Farbe
    });
  }
  const additionalStyles = document.createElement('style');
  additionalStyles.textContent = `
    #voucher-input::placeholder {
      color: rgba(255, 255, 255, 0.7) !important;
      font-size: 1.1rem;
    }

    #voucher-input:focus {
      outline: none !important;
      border-color: rgba(255, 255, 255, 0.6) !important;
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2) !important;
    }
    
    #checkout-pay-button {
      position: relative;
      overflow: hidden;
      transform: translateZ(0);
      transition: all 0.15s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .ripple-circle {
      transform: scale(0);
      animation-name: button-ripple;
      animation-duration: 0.6s;
      animation-timing-function: ease-out;
    }
    
    @keyframes button-ripple {
      to {
        transform: scale(10);
        opacity: 0;
      }
    }

    #voucher-add-button:not(:disabled) {
      background: rgba(16, 185, 129, 0.8) !important;
      border-color: rgba(16, 185, 129, 0.6) !important;
      opacity: 1 !important;
    }

    #voucher-add-button:not(:disabled):hover {
      background: rgba(16, 185, 129, 1) !important;
      transform: scale(1.05);
      transition: all 0.2s ease;
    }
  `;
  document.head.appendChild(additionalStyles);

  // Event-Listener hinzufügen
  setupCheckoutEvents();

  console.log('✅ Checkout-Overlay erstellt');
}

function getCartItems() {
  const cartItems = [];
  const items = document.querySelectorAll('app-cart-item-wrapper');

  items.forEach(item => {
    const name = item.querySelector('.font-semibold')?.textContent?.trim() || 'Artikel';
    const price = item.querySelector('.text-end')?.textContent?.trim() || '0,00 €';
    
    // Extrahiere Anzahl aus dem weißen Bereich mit w-[10%]
    const quantityElement = item.querySelector('.w-\\[10\\%\\]');
    const quantity = quantityElement ? quantityElement.textContent?.trim() || '1' : '1';
    
    cartItems.push({ name, price, quantity });
  });

  return cartItems;
}

function calculateTotal(cartItems) {
  let total = 0;
  cartItems.forEach(item => {
    const price = parseFloat(item.price.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
    total += price;
  });
  return total;
}

// Neue Funktion: Hole angewendete Gutscheine aus dem Original-Frontend
function getAppliedVouchers() {
  const vouchers = [];

  // Methode 1: Suche nach Gutscheineinlösungen im Original-Frontend
  const voucherElements = document.querySelectorAll('p.text-lg');
  for (const element of voucherElements) {
    const text = element.textContent || '';
    if (text.includes('Gutscheineinlösung')) {
      // Suche nach dem Betrag im nächsten Element
      const parent = element.closest('.w-full.flex.justify-between.py-4');
      if (parent) {
        const amountElement = parent.querySelector('.text-red-500.text-lg');
        if (amountElement) {
          const amount = amountElement.textContent.trim();
          vouchers.push({
            name: text.trim(),
            amount: amount
          });
        }
      }
    }
  }

  // Methode 2: Suche nach allen Elementen mit rotem Text (Rabatte)
  const redTextElements = document.querySelectorAll('.text-red-500');
  for (const element of redTextElements) {
    const text = element.textContent || '';
    if (text.includes('-') && text.includes('€')) {
      // Prüfe ob es sich um einen Gutschein handelt
      const parent = element.closest('.w-full.flex.justify-between.py-4');
      if (parent) {
        const labelElement = parent.querySelector('p.text-lg');
        if (labelElement && labelElement.textContent.includes('Gutscheineinlösung')) {
          // Bereits in Methode 1 erfasst
          continue;
        }
        if (labelElement && (
          labelElement.textContent.includes('Gutschein') ||
          labelElement.textContent.includes('Rabatt') ||
          labelElement.textContent.includes('Discount')
        )) {
          vouchers.push({
            name: labelElement.textContent.trim(),
            amount: text.trim()
          });
        }
      }
    }
  }

  console.log('🎫 Gefundene Gutscheine:', vouchers);
  return vouchers;
}

// Neue Funktion: Berechne Gutschein-Rabatt
function calculateVoucherDiscount(vouchers) {
  let discount = 0;
  vouchers.forEach(voucher => {
    // Extrahiere Betrag aus dem Format "-21,90 €"
    const amount = voucher.amount.replace(/[^\d,]/g, '').replace(',', '.');
    discount += parseFloat(amount) || 0;
  });
  return discount;
}

// Neue Funktion: Aktualisiere das Checkout-Overlay
function updateCheckoutOverlay() {
  console.log('🔄 Aktualisiere Checkout-Overlay...');

  const overlay = document.getElementById('checkout-overlay');
  if (!overlay) {
    console.log('❌ Checkout-Overlay nicht gefunden');
    return;
  }

  // Hole aktuelle Daten
  const cartItems = getCartItems();
  const vouchers = getAppliedVouchers();
  const subtotal = calculateTotal(cartItems);
  const voucherDiscount = calculateVoucherDiscount(vouchers);
  const finalTotal = subtotal - voucherDiscount;

  // Aktualisiere Netto-Betrag
  const nettoElement = overlay.querySelector('.netto-amount');
  if (nettoElement) {
    nettoElement.textContent = `${subtotal.toFixed(2).replace('.', ',')} €`;
  }

  // Aktualisiere Brutto-Betrag
  const bruttoElement = overlay.querySelector('.brutto-amount');
  if (bruttoElement) {
    bruttoElement.textContent = `${subtotal.toFixed(2).replace('.', ',')} €`;
  }

  // Aktualisiere Gutschein-Bereich
  const voucherContainer = overlay.querySelector('#voucher-container');
  if (voucherContainer) {
    if (vouchers.length > 0) {
      voucherContainer.innerHTML = vouchers.map(voucher => `
        <div style="display: flex; justify-content: space-between; padding: 0.8rem 0; border-bottom: 1px solid rgba(255,255,255,0.2); background: rgba(255,255,255,0.1); border-radius: 8px; margin-bottom: 0.5rem; padding-left: 1rem; padding-right: 1rem;">
          <p style="font-size: 1.2rem; margin: 0; color: white; font-weight: 600;">${voucher.name}</p>
          <p style="font-size: 1.2rem; margin: 0; color: #ff6b6b; font-weight: 700;">${voucher.amount}</p>
        </div>
      `).join('');
      voucherContainer.style.display = 'block';
    } else {
      voucherContainer.style.display = 'none';
    }
  }

  // Aktualisiere Gesamtsumme
  const totalElement = overlay.querySelector('.final-total');
  if (totalElement) {
    totalElement.textContent = `${finalTotal.toFixed(2).replace('.', ',')} €`;
  }

  console.log(`✅ Overlay aktualisiert - Subtotal: ${subtotal}€, Rabatt: ${voucherDiscount}€, Final: ${finalTotal}€`);
}

// Neue Funktion: Observer für automatische Aktualisierung
function setupCheckoutUpdateObserver() {
  console.log('👁️ Richte Observer für Checkout-Aktualisierung ein...');

  // Verhindere mehrfache Observer-Erstellung
  if (window.checkoutUpdateObserver) {
    console.log('Observer bereits vorhanden, überspringe Erstellung');
    return;
  }

  let lastUpdateTime = 0;
  const UPDATE_THROTTLE = 1000; // Mindestens 1 Sekunde zwischen Updates

  // Observer für Änderungen im Original-Frontend
  const observer = new MutationObserver((mutations) => {
    let shouldUpdate = false;
    const now = Date.now();

    // Throttle Updates um endlose Schleifen zu vermeiden
    if (now - lastUpdateTime < UPDATE_THROTTLE) {
      return;
    }

    mutations.forEach((mutation) => {
      // Ignoriere Änderungen im Kiosk-Overlay selbst
      if (mutation.target.closest && mutation.target.closest('#checkout-overlay')) {
        return;
      }

      // Prüfe auf Änderungen in Gutschein-relevanten Bereichen
      if (mutation.type === 'childList' || mutation.type === 'characterData') {
        const target = mutation.target;

        // Prüfe auf Gutscheineinlösungen nur im Original-Frontend
        if (target.textContent && target.textContent.includes('Gutscheineinlösung') &&
            !target.closest('#checkout-overlay')) {
          console.log('🎫 Gutscheineinlösung im Original-Frontend erkannt');
          shouldUpdate = true;
        }

        // Prüfe auf Änderungen in checkout-details (nur Original)
        if (target.closest && target.closest('app-checkout-details') &&
            !target.closest('#checkout-overlay')) {
          shouldUpdate = true;
        }
      }
    });

    if (shouldUpdate) {
      lastUpdateTime = now;
      // Verzögere die Aktualisierung etwas, um mehrere schnelle Änderungen zu sammeln
      clearTimeout(window.checkoutUpdateTimeout);
      window.checkoutUpdateTimeout = setTimeout(() => {
        updateCheckoutOverlay();
      }, 500);
    }
  });

  // Überwache nur das Original-Frontend, nicht das Overlay
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    observer.observe(appRoot, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: false
    });
  }

  // Speichere Observer für spätere Bereinigung
  window.checkoutUpdateObserver = observer;

  console.log('✅ Checkout-Update-Observer eingerichtet');
}

function closeCheckoutOverlay() {
  const overlay = document.getElementById('checkout-overlay');
  if (overlay) {
    overlay.remove();
    console.log('Checkout-Overlay geschlossen.');
    if (checkoutObserver) {
      checkoutObserver.disconnect();
      checkoutObserver = null;
      console.log('Checkout-Observer gestoppt.');
    }

    // Bereinige auch den Update-Observer
    if (window.checkoutUpdateObserver) {
      window.checkoutUpdateObserver.disconnect();
      window.checkoutUpdateObserver = null;
      console.log('Checkout-Update-Observer gestoppt.');
    }

    // Bereinige Timeout
    if (window.checkoutUpdateTimeout) {
      clearTimeout(window.checkoutUpdateTimeout);
      window.checkoutUpdateTimeout = null;
    }
  }
}

function setupCheckoutEvents() {
  const payButton = document.getElementById('checkout-pay-button');
  const clearCartButton = document.getElementById('checkout-clear-cart-button');
  const closeButton = document.getElementById('close-checkout-overlay');
  const voucherInput = document.getElementById('voucher-input');
  const voucherAddButton = document.getElementById('voucher-add-button');

  // Observer für automatische Aktualisierung des Overlays
  setupCheckoutUpdateObserver();

  if (closeButton) {
    closeButton.addEventListener('click', closeCheckoutOverlay);
  }

  if (payButton) {
    payButton.addEventListener('click', () => {
      console.log('💳 EC-Zahlung gestartet - suche Original EC-Button');
      
      // Original-Interface einblenden für Button-Suche
      const appRoot = document.querySelector('app-root');
      if (appRoot) {
        appRoot.style.setProperty('display', 'block', 'important');
        console.log('Original-Interface für EC-Zahlung eingeblendet');
      }
      
      // Warte kurz, dann suche Original EC-Button
      setTimeout(() => {
        console.log('🔍 Suche nach Original EC-Button...');
        
        // Suche nach dem Original EC-Button mit fa-credit-card Icon
        const originalEcButton = document.querySelector('button .fa-credit-card');
        
        if (originalEcButton) {
          console.log('✅ Original EC-Button gefunden!');
          
          // Klicke auf den Parent-Button
          const button = originalEcButton.closest('button');
          if (button) {
            console.log('Klicke auf EC-Button');
            button.click();
            
            // Zusätzlicher Event-basierter Click für Sicherheit
            setTimeout(() => {
              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
              });
              button.dispatchEvent(clickEvent);
              console.log('Zusätzlicher EC-Button Event-Click ausgeführt');
            }, 100);
          }
        } else {
          console.log('❌ Original EC-Button nicht gefunden, verwende Fallback-Suche');
          
          // Fallback: Suche nach Buttons mit "EC" Text oder credit-card Icon
          const allButtons = document.querySelectorAll('button');
          for (const button of allButtons) {
            const hasEcText = button.textContent.includes('EC');
            const hasCreditIcon = button.querySelector('i[class*="credit-card"], i.fa-credit-card');
            
            if (hasEcText || hasCreditIcon) {
              console.log('✅ EC-Button via Fallback gefunden');
              button.click();
              break;
            }
          }
        }
        
        // Interface nach EC-Klick wieder verstecken
        setTimeout(() => {
          if (appRoot) {
            appRoot.style.setProperty('display', 'none', 'important');
            console.log('Original-Interface nach EC-Klick wieder versteckt');
          }
        }, 500);
        
      }, 300);
    });

    payButton.addEventListener('mouseenter', () => {
      payButton.style.background = '#047857';
      payButton.style.transform = 'scale(0.98)';
    });

    payButton.addEventListener('mouseleave', () => {
      payButton.style.background = '#059669';
      payButton.style.transform = 'scale(1)';
    });
  }

  if (clearCartButton) {
    clearCartButton.addEventListener('click', () => {
      console.log('🗑️ Warenkorb leeren');

      // Detaillierte Suche nach dem Original-Papierkorb-Button
      console.log('🔍 Suche nach Original-Papierkorb-Button...');

      // Original-Interface einblenden für bessere Button-Suche
      const appRoot = document.querySelector('app-root');
      if (appRoot) {
        appRoot.style.setProperty('display', 'block', 'important');
        console.log('Original-Interface eingeblendet für Button-Suche');
      }

      // Warte kurz, dann suche Button
      setTimeout(() => {
        // Mehrere Suchmethoden für den Papierkorb-Button
        let originalTrashButton = null;

        // Methode 1: Suche nach fa-trash Icon
        originalTrashButton = Array.from(document.querySelectorAll('button')).find(btn => {
          const icon = btn.querySelector('i.fa-trash, i.fal.fa-trash, i.fas.fa-trash');
          return icon !== null;
        });

        if (!originalTrashButton) {
          // Methode 2: Suche nach trash in Klassen
          originalTrashButton = Array.from(document.querySelectorAll('button')).find(btn => {
            const icon = btn.querySelector('i[class*="trash"]');
            return icon !== null;
          });
        }

        if (!originalTrashButton) {
          // Methode 3: Suche nach Button mit trash im HTML
          originalTrashButton = Array.from(document.querySelectorAll('button')).find(btn => {
            return btn.innerHTML.includes('trash') || btn.innerHTML.includes('fa-trash');
          });
        }

        // Debug: Alle Buttons loggen
        const allButtons = document.querySelectorAll('button');
        console.log(`🔍 Gefunden: ${allButtons.length} Buttons insgesamt`);
        allButtons.forEach((btn, index) => {
          const icon = btn.querySelector('i');
          const iconClass = icon ? icon.className : 'kein Icon';
          console.log(`Button ${index}: "${btn.textContent?.trim()}" - Icon: ${iconClass}`);
        });

        if (originalTrashButton) {
          console.log('✅ Original-Papierkorb-Button gefunden!');
          console.log('Button HTML:', originalTrashButton.outerHTML);

          // Mehrere Click-Methoden versuchen
          console.log('🖱️ Versuche verschiedene Click-Methoden...');

          // Methode 1: Direkter Click
          try {
            originalTrashButton.click();
            console.log('✅ Direkter Click ausgeführt');
          } catch (e) {
            console.log('❌ Direkter Click fehlgeschlagen:', e);
          }

          // Methode 2: Event-basierter Click
          setTimeout(() => {
            try {
              const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
              });
              originalTrashButton.dispatchEvent(clickEvent);
              console.log('✅ Event-basierter Click ausgeführt');
            } catch (e) {
              console.log('❌ Event-basierter Click fehlgeschlagen:', e);
            }
          }, 100);

          // Methode 3: Focus + Enter
          setTimeout(() => {
            try {
              originalTrashButton.focus();
              const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                bubbles: true
              });
              originalTrashButton.dispatchEvent(enterEvent);
              console.log('✅ Focus + Enter ausgeführt');
            } catch (e) {
              console.log('❌ Focus + Enter fehlgeschlagen:', e);
            }
          }, 200);

          // Warte auf Warenkorb-Leerung und erstelle dann komplett neues Overlay
          waitForCartClearAndRecreateOverlay();

        } else {
          console.log('❌ Original-Papierkorb-Button nicht gefunden');
          console.log('Verfügbare Buttons:');
          allButtons.forEach((btn, index) => {
            console.log(`  ${index}: ${btn.outerHTML.substring(0, 100)}...`);
          });
          alert('Warenkorb-Button konnte nicht gefunden werden');
        }
      }, 500); // Warte 500ms für DOM-Update
    });

    clearCartButton.addEventListener('mouseenter', () => {
      clearCartButton.style.background = '#b91c1c';
      clearCartButton.style.transform = 'scale(0.98)';
    });

    clearCartButton.addEventListener('mouseleave', () => {
      clearCartButton.style.background = '#dc2626';
      clearCartButton.style.transform = 'scale(1)';
    });
  }

  // Gutschein-Input Events
  if (voucherInput && voucherAddButton) {
    voucherInput.addEventListener('input', () => {
      const hasValue = voucherInput.value.trim().length > 0;
      voucherAddButton.disabled = !hasValue;
      voucherAddButton.style.opacity = hasValue ? '1' : '0.5';
      voucherAddButton.style.background = hasValue ? '#059669' : '#e5e7eb';
      voucherAddButton.style.color = hasValue ? 'white' : '#6b7280';
      voucherAddButton.style.cursor = hasValue ? 'pointer' : 'not-allowed';
    });

    // Flag, um zu verhindern, dass mehrere Gutschein-Anfragen parallel laufen
    let isProcessingVoucher = false;
    
    voucherAddButton.addEventListener('click', () => {
      // Prüfen, ob der Button nicht deaktiviert ist und keine Anfrage läuft
      if (!voucherAddButton.disabled && !isProcessingVoucher) {
        isProcessingVoucher = true; // Setze Flag, dass Gutschein verarbeitet wird

        const voucherCode = voucherInput.value.trim();
        console.log('🎫 Gutschein eingegeben:', voucherCode);

        // Zeige Ladeanimation
        createVoucherLoadingOverlay();

        // UI sofort zurücksetzen, um zu verhindern, dass der Benutzer erneut klickt
        voucherInput.value = '';
        voucherAddButton.disabled = true;
        voucherAddButton.style.opacity = '0.5';
        voucherAddButton.style.background = '#e5e7eb';
        voucherAddButton.style.color = '#6b7280';
        
        // Original-Interface vorübergehend einblenden für Input-Suche
        const appRoot = document.querySelector('app-root');
        if (appRoot) {
          appRoot.style.setProperty('display', 'block', 'important');
          console.log('Original-Interface für Gutschein-Eingabe eingeblendet');
        }
        
        // Suche nach dem original Gutschein-Input-Feld
        setTimeout(() => {
          // Umfassendere Suche nach dem Gutschein-Input mit verschiedenen Selektoren
          let originalVoucherInput = document.querySelector('input[placeholder="Gutschein- oder Rabattcode eingeben"]');
          
          // Fallback-Selektoren, falls der erste nicht funktioniert
          if (!originalVoucherInput) {
            originalVoucherInput = document.querySelector('input[placeholder*="Gutschein"]');
          }
          
          if (!originalVoucherInput) {
            originalVoucherInput = document.querySelector('input[placeholder*="voucher"]') ||
                                  document.querySelector('input[placeholder*="Rabatt"]') ||
                                  document.querySelector('input[name*="voucher"]') ||
                                  document.querySelector('input[name*="gutschein"]') ||
                                  document.querySelector('input[class*="voucher"]') ||
                                  document.querySelector('input[class*="gutschein"]');
          }
          
          if (originalVoucherInput) {
            console.log('✅ Original Gutschein-Input-Feld gefunden!');
            
            // Setze den Wert des Original-Input-Felds
            originalVoucherInput.value = voucherCode;
            
            // Löse Input-Event aus, um Angular's Change Detection zu triggern
            const inputEvent = new Event('input', { bubbles: true });
            originalVoucherInput.dispatchEvent(inputEvent);
            
            // Falls nötig, auch ein change-Event auslösen
            const changeEvent = new Event('change', { bubbles: true });
            originalVoucherInput.dispatchEvent(changeEvent);
            
            // Fokussiere das Feld
            originalVoucherInput.focus();
            
            console.log(`✅ Gutscheincode "${voucherCode}" wurde ins Original-Feld übertragen`);
            
            // Suche und klicke den Einlösen-Button
            setTimeout(() => {
              // Verschiedene mögliche Selektoren für den Einlösen-Button
              const voucherButtons = [
                // Button direkt neben dem Input
                originalVoucherInput.parentElement?.querySelector('button'),
                // Button mit Text "Einlösen" oder "Anwenden"
                ...Array.from(document.querySelectorAll('button')).filter(btn => 
                  btn.textContent && ['einlösen', 'anwenden', 'apply', 'redeem'].some(
                    text => btn.textContent.toLowerCase().includes(text)
                  )
                ),
                // Button in der Nähe des Eingabefelds
                ...Array.from(document.querySelectorAll('button')).filter(btn => {
                  const rect1 = originalVoucherInput.getBoundingClientRect();
                  const rect2 = btn.getBoundingClientRect();
                  return Math.abs(rect1.right - rect2.left) < 50 && Math.abs(rect1.top - rect2.top) < 30;
                })
              ];
              
              // Den ersten gefundenen Button klicken
              const voucherButton = voucherButtons.find(btn => btn);
              if (voucherButton) {
                console.log('✅ Gutschein-Einlösen-Button gefunden und wird geklickt');
                voucherButton.click();
              } else {
                console.log('⚠️ Gutschein-Einlösen-Button nicht gefunden');
              }
              
              // Starte einen Observer, der nach der Gutscheineinlösung sucht
              checkForVoucherRedemption(voucherCode);
            }, 500);
          } else {
            console.log('❌ Original Gutschein-Input-Feld nicht gefunden');
            alert(`Gutscheinfeld nicht gefunden. Code war: "${voucherCode}"`);
            removeVoucherLoadingOverlay(); // Entferne Ladeanimation bei Fehler
            isProcessingVoucher = false; // Setze Flag zurück bei Fehler
          }
          
          // Interface nach Gutschein-Übertragung wieder verstecken
          // Ausreichend Zeit geben, um die Einlösung zu erkennen
          setTimeout(() => {
            if (appRoot) {
              appRoot.style.setProperty('display', 'none', 'important');
              console.log('Original-Interface nach Gutschein-Eingabe wieder versteckt');
            }
            
            // Nach einer angemessenen Zeit das Flag zurücksetzen, um neue Eingaben zu ermöglichen
            setTimeout(() => {
              isProcessingVoucher = false;
              removeVoucherLoadingOverlay(); // Entferne Ladeanimation
              console.log('✅ Gutscheinverarbeitung abgeschlossen, bereit für neue Eingabe');
            }, 5000); // 5 Sekunden warten, bevor neue Eingaben erlaubt werden
          }, 3000);
        }, 500); // Erhöhte Zeit für stabilere Erkennung
      }
    });

    // Enter-Taste im Gutschein-Input
    voucherInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !voucherAddButton.disabled) {
        voucherAddButton.click();
      }
    });
  }
}



function startUrlMonitoring(appRoot, kioskOverlay) {
  console.log('🌐 Starte URL-Überwachung...');

  const initialUrl = window.location.href;
  console.log('Start-URL:', initialUrl);

  let urlCheckCount = 0;
  const maxUrlChecks = 20; // 10 Sekunden überwachen

  const checkUrlChange = () => {
    urlCheckCount++;
    const currentUrl = window.location.href;

    if (currentUrl !== initialUrl) {
      console.log(`🔄 URL-Änderung erkannt nach ${urlCheckCount * 500}ms:`);
      console.log('Vorher:', initialUrl);
      console.log('Nachher:', currentUrl);

      // Analysiere die neue URL
      if (currentUrl.includes('checkout')) {
        console.log('✅ URL enthält "checkout" - das ist gut!');
      } else if (currentUrl.includes('wizard')) {
        console.log('⚠️ URL enthält "wizard" - möglicherweise Addon-Auswahl');
        console.log('Versuche Wizard zu überspringen...');
        skipWizardIfPossible();
      } else if (currentUrl.includes('addons')) {
        console.log('⚠️ URL enthält "addons" - Addon-Auswahl erkannt');
        console.log('Versuche Addons zu überspringen...');
        skipAddonsIfPossible();
      } else {
        console.log('❓ Unbekannte URL-Änderung');
      }

      return; // Stop monitoring after URL change
    }

    if (urlCheckCount < maxUrlChecks) {
      setTimeout(checkUrlChange, 500);
    } else {
      console.log('⏰ URL-Überwachung beendet - keine Änderung erkannt');
    }
  };

  // Start monitoring after 1 second
  setTimeout(checkUrlChange, 1000);
}

function skipWizardIfPossible() {
  console.log('🚀 Versuche Wizard zu überspringen...');

  // Suche nach "Weiter", "Überspringen", "Skip" Buttons
  const skipSelectors = [
    'button:contains("Weiter")',
    'button:contains("Überspringen")',
    'button:contains("Skip")',
    'button:contains("Fortfahren")',
    'button:contains("Checkout")',
    '[class*="next"] button',
    '[class*="skip"] button',
    '[class*="continue"] button'
  ];

  const allButtons = document.querySelectorAll('button');
  for (const button of allButtons) {
    const text = button.textContent?.toLowerCase() || '';
    if (text.includes('weiter') || text.includes('überspringen') ||
        text.includes('skip') || text.includes('fortfahren') ||
        text.includes('checkout') || text.includes('continue')) {
      console.log(`🎯 Klicke auf: "${button.textContent}"`);
      button.click();
      return;
    }
  }

  console.log('❌ Kein Skip-Button gefunden');
}

function skipAddonsAutomatically() {
  console.log('🚀 Überspringe Addons automatisch...');

  // Verstecke die Addon-Seite sofort
  const addonWizard = document.querySelector('app-product-wizard');
  if (addonWizard) {
    addonWizard.style.setProperty('display', 'none', 'important');
    console.log('Addon-Wizard versteckt');
  }

  // Starte kontinuierliche Suche nach dem Weiter-Button
  startContinuousWeiterButtonSearch();
}

function startContinuousWeiterButtonSearch() {
  console.log('🔍 Starte kontinuierliche Suche nach Weiter-Button...');

  let attempts = 0;
  const maxAttempts = 20; // 10 Sekunden suchen

  const searchAndClick = () => {
    attempts++;
    console.log(`Weiter-Button Suche Versuch ${attempts}/${maxAttempts}`);

    // Suche nach dem spezifischen Weiter-Button
    const weiterButton = Array.from(document.querySelectorAll('button')).find(btn => {
      const text = btn.textContent?.trim() || '';
      return text === 'Weiter' || text.toLowerCase() === 'weiter';
    });

    if (weiterButton) {
      console.log('✅ "Weiter" Button gefunden, klicke sofort!');

      // Sofortiger Click ohne Verzögerung
      try {
        weiterButton.click();
        console.log('✅ Direkter Click auf Weiter-Button ausgeführt');
      } catch (e) {
        console.log('❌ Direkter Click fehlgeschlagen:', e.message);
      }

      // Zusätzlicher Event-Click nach 50ms
      setTimeout(() => {
        try {
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
          });
          weiterButton.dispatchEvent(clickEvent);
          console.log('✅ Event-basierter Click auf Weiter-Button ausgeführt');
        } catch (e) {
          console.log('❌ Event-basierter Click fehlgeschlagen:', e.message);
        }
      }, 50);

      // Focus + Enter als zusätzliche Sicherheit
      setTimeout(() => {
        try {
          weiterButton.focus();
          const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            bubbles: true
          });
          weiterButton.dispatchEvent(enterEvent);
          console.log('✅ Focus + Enter auf Weiter-Button ausgeführt');
        } catch (e) {
          console.log('❌ Focus + Enter fehlgeschlagen:', e.message);
        }
      }, 100);

      return; // Button gefunden und geklickt, stoppe Suche
    }

    // Fallback: Suche nach Button mit "Weiter" im Text
    const allButtons = document.querySelectorAll('button');
    for (const button of allButtons) {
      const text = button.textContent?.toLowerCase() || '';
      if (text.includes('weiter')) {
        console.log(`🎯 Fallback: Weiter-Button gefunden: "${button.textContent}"`);
        button.click();
        return;
      }
    }

    if (attempts < maxAttempts) {
      // Weiter suchen alle 500ms
      setTimeout(searchAndClick, 500);
    } else {
      console.log('❌ Weiter-Button nicht gefunden nach 10 Sekunden');

      // Letzter Ausweg: Klicke auf irgendeinen Button
      if (allButtons.length > 0) {
        console.log(`🎯 Letzter Ausweg: Klicke auf ersten Button: "${allButtons[0].textContent}"`);
        allButtons[0].click();
      }
    }
  };

  // Starte sofort die Suche
  searchAndClick();
}

function skipAddonsIfPossible() {
  // Diese Funktion wird durch skipAddonsAutomatically ersetzt
  skipAddonsAutomatically();
}

function getStartUrlFromConfig() {
  console.log('🔍 Suche Start-URL in der Konfiguration...');

  try {
    // Methode 1: Aus der aktuellen URL extrahieren
    const currentUrl = window.location.href;
    console.log('Aktuelle URL:', currentUrl);

    // Extrahiere die Basis-URL bis zum Shop-Teil
    const urlParts = currentUrl.split('/');
    if (urlParts.length >= 6) {
      // Format: https://pos-shop.wizid.com/p/saddasddsa/shop_X0k_pmXj6brKZaK7uIYx7/...
      const baseUrl = urlParts.slice(0, 6).join('/');
      console.log('✅ Start-URL aus aktueller URL extrahiert:', baseUrl);
      return baseUrl;
    }

    // Methode 2: Aus localStorage/sessionStorage
    const storedUrl = localStorage.getItem('kioskStartUrl') || sessionStorage.getItem('kioskStartUrl');
    if (storedUrl) {
      console.log('✅ Start-URL aus Storage gefunden:', storedUrl);
      return storedUrl;
    }

    // Methode 3: Aus Meta-Tags oder anderen DOM-Elementen
    const metaUrl = document.querySelector('meta[name="start-url"]')?.content;
    if (metaUrl) {
      console.log('✅ Start-URL aus Meta-Tag gefunden:', metaUrl);
      return metaUrl;
    }

    // Methode 4: Aus der Electron-Konfiguration
    if (window.kioskAPI && window.kioskAPI.getStartUrl) {
      const apiUrl = window.kioskAPI.getStartUrl();
      if (apiUrl) {
        console.log('✅ Start-URL aus API gefunden:', apiUrl);
        return apiUrl;
      }
    }

    console.log('❌ Keine Start-URL gefunden, verwende Fallback');
    return null;

  } catch (error) {
    console.error('❌ Fehler beim Ermitteln der Start-URL:', error);
    return null;
  }
}

function saveStartUrl() {
  // Speichere die aktuelle URL als Start-URL für spätere Verwendung
  const currentUrl = window.location.href;
  const urlParts = currentUrl.split('/');

  if (urlParts.length >= 6) {
    const startUrl = urlParts.slice(0, 6).join('/');
    localStorage.setItem('kioskStartUrl', startUrl);
    sessionStorage.setItem('kioskStartUrl', startUrl);
    console.log('💾 Start-URL gespeichert:', startUrl);
  }
}

function startCartMonitoring() {
  console.log('📊 Starte kontinuierliches Warenkorb-Monitoring...');
  
  let lastCartCount = -1;
  let lastCartItemsSignature = '';
  
  const checkCartChanges = () => {
    // Nur prüfen wenn wir auf der Shop-Seite sind (nicht im Checkout)
    const isOnShopPage = document.querySelector('app-shop') && !document.querySelector('app-checkout');
    const kioskOverlay = document.getElementById('kiosk-overlay');
    
    if (isOnShopPage && kioskOverlay && kioskOverlay.style.display !== 'none') {
      const cartItems = document.querySelectorAll('app-cart-item-wrapper');
      const currentCartCount = cartItems.length;
      
      // Erstelle Signatur der aktuellen Warenkorb-Items
      let currentSignature = '';
      cartItems.forEach(item => {
        const title = item.querySelector('.font-semibold')?.textContent?.trim() || '';
        const price = item.querySelector('.text-end')?.textContent?.trim() || '';
        currentSignature += `${title}:${price};`;
      });
      
      // Prüfe ob sich etwas geändert hat
      if (currentCartCount !== lastCartCount || currentSignature !== lastCartItemsSignature) {
        console.log(`📊 Warenkorb-Änderung erkannt: ${lastCartCount} → ${currentCartCount} Items`);
        console.log('📊 Aktualisiere Kiosk-Warenkorb...');
        
        // Warenkorb sofort aktualisieren
        loadCart();
        
        // Werte speichern
        lastCartCount = currentCartCount;
        lastCartItemsSignature = currentSignature;
        
        // Zusätzliche Aktualisierung nach kurzer Verzögerung
        setTimeout(() => {
          console.log('📊 Nachträgliche Warenkorb-Aktualisierung...');
          loadCart();
        }, 500);
      }
    }
  };
  
  // Prüfung alle 1 Sekunde
  setInterval(checkCartChanges, 1000);
  
  // MutationObserver für sofortige Reaktion auf DOM-Änderungen
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      // Prüfe auf Änderungen an Warenkorb-Elementen
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && 
              (node.tagName === 'APP-CART-ITEM-WRAPPER' || 
               node.querySelector && node.querySelector('app-cart-item-wrapper'))) {
            console.log('📊 Warenkorb-Element hinzugefügt via MutationObserver');
            setTimeout(checkCartChanges, 100);
          }
        });
        
        mutation.removedNodes.forEach((node) => {
          if (node.nodeType === 1 && 
              (node.tagName === 'APP-CART-ITEM-WRAPPER' || 
               node.querySelector && node.querySelector('app-cart-item-wrapper'))) {
            console.log('📊 Warenkorb-Element entfernt via MutationObserver');
            setTimeout(checkCartChanges, 100);
          }
        });
      }
    });
  });
  
  // Observer auf Shopping Cart Container anwenden
  const shoppingCart = document.querySelector('app-shopping-cart');
  if (shoppingCart) {
    observer.observe(shoppingCart, {
      childList: true,
      subtree: true
    });
    console.log('✅ MutationObserver für Warenkorb gestartet');
  } else {
    // Fallback: Observer auf Body
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    console.log('✅ MutationObserver für Warenkorb gestartet (Body-Fallback)');
  }
}

function startAddonObserver() {
  console.log('🔍 Starte Addon-Überwachung mit MutationObserver...');

  // MutationObserver für DOM-Änderungen
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      // Prüfe auf neue Nodes
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // Prüfe ob Addon-Elemente hinzugefügt wurden
          if (node.tagName === 'APP-PRODUCT-WIZARD' ||
              node.querySelector && node.querySelector('app-product-wizard')) {
            console.log('🚨 Addon-Wizard erkannt via MutationObserver!');
            setTimeout(() => skipAddonsAutomatically(), 100);
          }

          if (node.tagName === 'APP-ADDONS' ||
              node.querySelector && node.querySelector('app-addons')) {
            console.log('🚨 Addon-Component erkannt via MutationObserver!');
            setTimeout(() => skipAddonsAutomatically(), 100);
          }

          // Prüfe auf Weiter-Button
          if (node.tagName === 'BUTTON' &&
              node.textContent && node.textContent.trim().toLowerCase() === 'weiter') {
            console.log('🚨 Weiter-Button erkannt via MutationObserver!');
            setTimeout(() => {
              console.log('🎯 Klicke sofort auf erkannten Weiter-Button');
              node.click();
            }, 50);
          }
        }
      });
    });
  });

  // Observer starten
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('✅ Addon-Observer gestartet');

  // Zusätzlich: Periodische Prüfung alle 500ms
  const intervalCheck = setInterval(() => {
    const addonWizard = document.querySelector('app-product-wizard');
    const addonComponent = document.querySelector('app-addons');
    const weiterButton = Array.from(document.querySelectorAll('button')).find(btn =>
      btn.textContent && btn.textContent.trim().toLowerCase() === 'weiter'
    );

    if (addonWizard || addonComponent) {
      console.log('🚨 Addon-Seite erkannt via Interval-Check!');
      skipAddonsAutomatically();
    }

    if (weiterButton) {
      console.log('🚨 Weiter-Button erkannt via Interval-Check!');
      weiterButton.click();
      console.log('🎯 Weiter-Button geklickt via Interval-Check');
    }
  }, 500);

  // Cleanup nach 30 Sekunden
  setTimeout(() => {
    clearInterval(intervalCheck);
    observer.disconnect();
    console.log('🔄 Addon-Observer nach 30s gestoppt');
  }, 30000);
}

function startOpenTransactionMonitor() {
  console.log('🔍 Starte Überwachung für offene Transaktionen...');
  
  // MutationObserver für das Erkennen des Transaktionsbildschirms
  const observer = new MutationObserver(() => {
    // Suche nach dem Transaktionsabbruch-Bildschirm
    const transactionDialog = document.querySelector('app-shop-init .max-w-xl.w-full.mx-4.bg-white.border.border-red-200');
    
    if (transactionDialog) {
      // Suche nach der Überschrift "Es wurde eine offene Transaktion gefunden"
      const heading = transactionDialog.querySelector('h2.text-xl.font-bold');
      
      if (heading && heading.textContent.includes('offene Transaktion gefunden')) {
        console.log('🚨 Offene Transaktion erkannt, suche nach Abbrechen-Button...');
        
        // Suche nach dem Abbrechen-Button
        const cancelButton = transactionDialog.querySelector('button.flex-1.bg-red-600');
        
        if (cancelButton && cancelButton.textContent.includes('Transaktion abbrechen')) {
          console.log('🔴 Klicke automatisch auf "Transaktion abbrechen"');
          
          // Kurze Verzögerung vor dem Klick, um UI-Probleme zu vermeiden
          setTimeout(() => {
            cancelButton.click();
            console.log('✅ Transaktion wurde automatisch abgebrochen');
          }, 500);
        }
      }
    }
  });
  
  // Observer auf dem Body starten, um Änderungen zu überwachen
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true
  });
  
  // Zusätzlich: Periodische Prüfung alle 500ms für mehr Zuverlässigkeit
  setInterval(() => {
    const transactionDialog = document.querySelector('app-shop-init .max-w-xl.w-full.mx-4.bg-white.border.border-red-200');
    if (transactionDialog) {
      const heading = transactionDialog.querySelector('h2.text-xl.font-bold');
      if (heading && heading.textContent.includes('offene Transaktion gefunden')) {
        const cancelButton = transactionDialog.querySelector('button.flex-1.bg-red-600');
        if (cancelButton && cancelButton.textContent.includes('Transaktion abbrechen')) {
          console.log('🔴 Intervall-Prüfung: Klicke auf "Transaktion abbrechen"');
          cancelButton.click();
        }
      }
    }
  }, 500);
  
  console.log('✅ Überwachung für offene Transaktionen gestartet');
}

function hideCheckoutElements() {
  console.log('Verstecke Checkout-Elemente (Betrag erhalten, Rückgeld)');

  // Diese Funktion wird durch CSS ersetzt - keine direkten DOM-Manipulationen mehr
  // Die Elemente werden über CSS-Selektoren versteckt
  console.log('Checkout-Elemente werden über CSS versteckt');
}

// Prüft, ob die Gutscheineinlösung erfolgreich war
function checkForVoucherRedemption(voucherCode) {
  console.log('🔍 Prüfe auf erfolgreiche Gutscheineinlösung...');
  
  // Indikator, ob der Gutschein erfolgreich eingelöst wurde
  let redemptionSuccessful = false;
  let feedbackShown = false;
  
  // Sofort prüfen
  checkForRedemptionSuccess();
  
  // Regelmäßige Überprüfung alle 200ms (zusätzlich zum Observer)
  const intervalCheck = setInterval(() => {
    if (!redemptionSuccessful && !feedbackShown) {
      checkForRedemptionSuccess();
    } else {
      clearInterval(intervalCheck);
    }
  }, 200);
  
  // Observer starten, um auf DOM-Änderungen zu reagieren
  const observer = new MutationObserver(() => {
    if (!redemptionSuccessful && !feedbackShown) {
      checkForRedemptionSuccess();
    }
  });
  
  // Observer auf den gesamten Body anwenden
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: true
  });
  
  // Nach 10 Sekunden prüfen, ob die Einlösung erfolgreich war. 
  // Falls nicht, zeige Fehlerfeedback an
  setTimeout(() => {
    observer.disconnect();
    clearInterval(intervalCheck);
    console.log(`🔍 Gutscheinprüfung beendet nach 10s - Erfolg: ${redemptionSuccessful ? 'Ja' : 'Nein'}`);
    
    // Wenn nach 10 Sekunden keine erfolgreiche Einlösung erkannt wurde,
    // gehen wir von einem Fehler aus
    if (!redemptionSuccessful && !feedbackShown) {
      console.log('❌ Keine erfolgreiche Gutscheineinlösung erkannt. Zeige Fehlerfeedback.');
      feedbackShown = true;
      removeVoucherLoadingOverlay(); // Entferne Ladeanimation bei Fehler
      handleVoucherErrorFeedbackForKiosk(); // Neue Funktion für das richtige Element verwenden
    } else if (redemptionSuccessful) {
      console.log('✅ Gutscheineinlösung erfolgreich!');
      removeVoucherLoadingOverlay(); // Entferne Ladeanimation bei Erfolg
      handleVoucherSuccessFeedbackForKiosk();
      // Aktualisiere das Overlay nach erfolgreicher Gutscheineinlösung
      setTimeout(() => {
        updateCheckoutOverlay();
      }, 1000);
    }
  }, 10000);
  
  // Hilfsfunktion, um nach dem Erfolgselement zu suchen
  function checkForRedemptionSuccess() {
    // Methode 1: Suche nach dem charakteristischen p.text-lg Element
    const successElements = document.querySelectorAll('p.text-lg');
    for (const element of successElements) {
      const text = element.textContent || '';
      if (text.includes('Gutscheineinlösung')) {
        console.log('✅ Erfolgreiche Gutscheineinlösung erkannt (Methode 1): "' + text + '"');
        redemptionSuccessful = true;
        return;
      }
    }
    
    // Methode 2: Suche nach einem Div mit dem charakteristischen Layout
    const divElements = document.querySelectorAll('.w-full.flex.justify-between.py-4');
    for (const divElement of divElements) {
      const pElement = divElement.querySelector('p.text-lg');
      if (pElement && pElement.textContent && pElement.textContent.includes('Gutscheineinlösung')) {
        console.log('✅ Erfolgreiche Gutscheineinlösung erkannt (Methode 2): "' + pElement.textContent + '"');
        redemptionSuccessful = true;
        return;
      }
    }
    
    // Methode 3: Suche im gesamten DOM-Text nach dem Muster
    const bodyText = document.body.textContent || '';
    if (bodyText.includes('Gutscheineinlösung') && bodyText.includes('-14,90')) {
      console.log('✅ Erfolgreiche Gutscheineinlösung erkannt (Methode 3): Text im Body gefunden');
      redemptionSuccessful = true;
      return;
    }
  }
}

function handleVoucherErrorFeedback() {
  // Finde das Gutschein-Eingabefeld im Kiosk-Overlay
  const voucherInput = document.querySelector('#kiosk-checkout-voucher-input');
  
  if (voucherInput) {
    // Eingabe löschen
    voucherInput.value = '';
    
    // Sehr auffällige rote Umrandung hinzufügen
    voucherInput.style.border = '4px solid #ef4444';
    voucherInput.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.9)';
    
    // Blinken-Animation hinzufügen
    let blinkCount = 0;
    const blinkInterval = setInterval(() => {
      blinkCount++;
      voucherInput.style.border = blinkCount % 2 === 0 ? '4px solid #ef4444' : '4px solid #b91c1c';
      voucherInput.style.boxShadow = blinkCount % 2 === 0 ? 
        '0 0 15px rgba(239, 68, 68, 0.9)' : 
        '0 0 20px rgba(185, 28, 28, 1)';
      
      if (blinkCount >= 8) { // Mehr Blinken für bessere Sichtbarkeit
        clearInterval(blinkInterval);
        // Nach dem Blinken: Halte die rote Umrandung für 3 Sekunden
        setTimeout(() => {
          // Zurück zum normalen Stil
          voucherInput.style.border = '2px solid #d1d5db';
          voucherInput.style.boxShadow = 'none';
        }, 3000);
      }
    }, 200);
    
    // Fokus auf das Eingabefeld setzen
    voucherInput.focus();
    
    console.log('🟥 Visuelles Feedback für ungültigen Gutschein angezeigt!');
  } else {
    console.log('❌ Kiosk-Gutschein-Eingabefeld nicht gefunden!');
  }
}

function handleVoucherErrorFeedback() {
  // Finde das Gutschein-Eingabefeld im Kiosk-Overlay
  const voucherInput = document.querySelector('#kiosk-checkout-voucher-input');
  
  if (voucherInput) {
    // Eingabe löschen
    voucherInput.value = '';
    
    // Sehr auffällige rote Umrandung hinzufügen
    voucherInput.style.border = '4px solid #ef4444';
    voucherInput.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.9)';
    
    // Blinken-Animation hinzufügen
    let blinkCount = 0;
    const blinkInterval = setInterval(() => {
      blinkCount++;
      voucherInput.style.border = blinkCount % 2 === 0 ? '4px solid #ef4444' : '4px solid #b91c1c';
      voucherInput.style.boxShadow = blinkCount % 2 === 0 ? 
        '0 0 15px rgba(239, 68, 68, 0.9)' : 
        '0 0 20px rgba(185, 28, 28, 1)';
      
      if (blinkCount >= 8) { // Mehr Blinken für bessere Sichtbarkeit
        clearInterval(blinkInterval);
        // Nach dem Blinken: Halte die rote Umrandung für 3 Sekunden
        setTimeout(() => {
          // Zurück zum normalen Stil
          voucherInput.style.border = '2px solid #d1d5db';
          voucherInput.style.boxShadow = 'none';
        }, 3000);
      }
    }, 200);
    
    // Fokus auf das Eingabefeld setzen
    voucherInput.focus();
    
    console.log('🟥 Visuelles Feedback für ungültigen Gutschein angezeigt!');
  } else {
    console.log('❌ Kiosk-Gutschein-Eingabefeld nicht gefunden!');
  }
}

function handleVoucherErrorFeedbackForKiosk() {
  // Finde das Gutschein-Eingabefeld mit der richtigen ID
  const voucherInput = document.querySelector('#voucher-input');
  
  if (voucherInput) {
    // Original-Platzhalter speichern
    const originalPlaceholder = voucherInput.placeholder;
    
    // Eingabe löschen
    voucherInput.value = '';
    
    // Platzhalter auf Fehlermeldung setzen
    voucherInput.placeholder = 'Gutscheinnummer ungültig';
    
    // Sehr auffällige rote Umrandung hinzufügen
    voucherInput.style.border = '3px solid #ef4444';
    voucherInput.style.boxShadow = '0 0 12px rgba(239, 68, 68, 0.8)';
    
    // Blinken-Animation hinzufügen
    let blinkCount = 0;
    const blinkInterval = setInterval(() => {
      blinkCount++;
      voucherInput.style.border = blinkCount % 2 === 0 ? '3px solid #ef4444' : '3px solid #b91c1c';
      voucherInput.style.boxShadow = blinkCount % 2 === 0 ? 
        '0 0 12px rgba(239, 68, 68, 0.8)' : 
        '0 0 15px rgba(185, 28, 28, 0.9)';
      
      if (blinkCount >= 6) {
        clearInterval(blinkInterval);
        // Nach dem Blinken: Halte die rote Umrandung für 3 Sekunden
        setTimeout(() => {
          // Zurück zum normalen Stil
          voucherInput.style.border = '2px solid #d1d5db';
          voucherInput.style.boxShadow = 'none';
          // Original-Platzhalter wiederherstellen
          voucherInput.placeholder = originalPlaceholder;
        }, 3000);
      }
    }, 200);
    
    // Fokus auf das Eingabefeld setzen
    voucherInput.focus();
    
    console.log('🔥 Visuelles Feedback für ungültigen Gutschein angezeigt!');
  } else {
    console.log('❌ Kiosk-Gutschein-Eingabefeld nicht gefunden! (ID: voucher-input)');
  }
}

function handleVoucherSuccessFeedbackForKiosk() {
  // Finde das Gutschein-Eingabefeld mit der richtigen ID
  const voucherInput = document.querySelector('#voucher-input');
  
  if (voucherInput) {
    // Original-Platzhalter speichern
    const originalPlaceholder = voucherInput.placeholder;
    
    // Eingabe löschen (optional)
    // voucherInput.value = '';
    
    // Platzhalter auf Erfolgsmeldung setzen
    voucherInput.placeholder = 'Gutschein eingelöst!';
    
    // Sehr auffällige grüne Umrandung hinzufügen
    voucherInput.style.border = '3px solid #10b981'; // Grüne Farbe
    voucherInput.style.boxShadow = '0 0 12px rgba(16, 185, 129, 0.8)';
    
    // Blinken-Animation hinzufügen
    let blinkCount = 0;
    const blinkInterval = setInterval(() => {
      blinkCount++;
      voucherInput.style.border = blinkCount % 2 === 0 ? '3px solid #10b981' : '3px solid #059669';
      voucherInput.style.boxShadow = blinkCount % 2 === 0 ? 
        '0 0 12px rgba(16, 185, 129, 0.8)' : 
        '0 0 15px rgba(5, 150, 105, 0.9)';
      
      if (blinkCount >= 6) {
        clearInterval(blinkInterval);
        // Nach dem Blinken: Halte die grüne Umrandung für 3 Sekunden
        setTimeout(() => {
          // Zurück zum normalen Stil
          voucherInput.style.border = '2px solid #d1d5db';
          voucherInput.style.boxShadow = 'none';
          // Original-Platzhalter wiederherstellen
          voucherInput.placeholder = originalPlaceholder;
        }, 3000);
      }
    }, 200);
    
    // Optional: Fokus auf das Eingabefeld setzen
    // voucherInput.focus();
    
    console.log('💚 Visuelles Feedback für erfolgreichen Gutschein angezeigt!');
  } else {
    console.log('❌ Kiosk-Gutschein-Eingabefeld nicht gefunden! (ID: voucher-input)');
  }
}

function hideUnnecessaryPaymentOptions() {
  console.log('Konfiguriere Zahlungsoptionen für Kiosk-Modus');
  
  // Prüfe ob bereits ein Style-Element existiert
  const existingStyle = document.getElementById('kiosk-payment-styles');
  if (existingStyle) {
    console.log('Payment-Style-Element bereits vorhanden, entferne es');
    existingStyle.remove();
  }

  const css = `
    /* Alle Zahlungsoptionen außer EC ausblenden */
    body:has(app-checkout) app-payment-options .flex.justify-stretch button:not(:has(.fa-credit-card)) {
      display: none !important;
    }

    /* EC-Button hervorheben und zentrieren */
    body:has(app-checkout) app-payment-options .flex.justify-stretch button:has(.fa-credit-card) {
      flex: 1 !important;
      min-height: 100px !important;
      font-size: 2rem !important;
      background: #047857 !important;
      color: white !important;
      border: 3px solid #065f46 !important;
      border-radius: 12px !important;
      font-weight: bold !important;
      margin: 0 auto !important;
      max-width: 300px !important;
    }

    /* EC-Button Hover-Effekt */
    body:has(app-checkout) app-payment-options .flex.justify-stretch button:has(.fa-credit-card):hover {
      background: #065f46 !important;
      transform: scale(0.98) !important;
    }

    /* Payment Options Container */
    body:has(app-checkout) app-payment-options .flex.justify-stretch {
      justify-content: center !important;
      align-items: center !important;
      gap: 0 !important;
    }
  `;

  const styleElement = document.createElement('style');
  styleElement.textContent = css;
  styleElement.id = 'kiosk-payment-styles';
  document.head.appendChild(styleElement);

  console.log('Payment-Styles für Kiosk-Modus hinzugefügt');

  // Optional: Log an Main Process (falls kioskAPI verfügbar)
  if (window.kioskAPI && window.kioskAPI.log) {
    window.kioskAPI.log('info', 'Zahlungsoptionen für Kiosk-Modus konfiguriert');
  }
}

function setupKioskBehavior() {
  console.log('Richte Kiosk-Verhalten ein');
  
  // Rechtsklick und Kontextmenü deaktivieren
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    return false;
  });
  
  // F12 und andere Dev-Keys deaktivieren (außer im Dev-Mode)
  document.addEventListener('keydown', (e) => {
    // F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    if (e.key === 'F12' || 
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
        (e.ctrlKey && e.key === 'u')) {
      e.preventDefault();
      return false;
    }
  });
  
  // Touch-Gesten optimieren
  document.addEventListener('touchstart', (e) => {
    // Prevent double-tap zoom
    if (e.touches.length > 1) {
      e.preventDefault();
    }
  }, { passive: false });
  
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (e) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
      e.preventDefault();
    }
    lastTouchEnd = now;
  }, false);
  
  // Drag & Drop deaktivieren
  document.addEventListener('dragstart', (e) => {
    e.preventDefault();
    return false;
  });
  
  // Textauswahl deaktivieren
  document.addEventListener('selectstart', (e) => {
    e.preventDefault();
    return false;
  });
  
  console.log('Kiosk-Verhalten konfiguriert');
  
  // Optional: Log an Main Process (falls kioskAPI verfügbar)
  if (window.kioskAPI && window.kioskAPI.log) {
    window.kioskAPI.log('info', 'Kiosk-Verhalten konfiguriert');
  }
}

// Error-Handler für unbehandelte Fehler
window.addEventListener('error', (e) => {
  console.error('Renderer-Fehler:', e);
  if (window.kioskAPI && window.kioskAPI.log) {
    window.kioskAPI.log('error', 'Renderer-Fehler', {
      message: e.message,
      filename: e.filename,
      lineno: e.lineno,
      colno: e.colno
    });
  }
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled Promise Rejection:', e.reason);
  if (window.kioskAPI && window.kioskAPI.log) {
    window.kioskAPI.log('error', 'Unhandled Promise Rejection', e.reason);
  }
});

function waitForCartClearAndRecreateOverlay() {
  console.log('⏳ Warte auf Warenkorb-Leerung und erstelle dann neues Overlay...');

  // Aktuelle Anzahl der Warenkorb-Items ermitteln
  const getCurrentCartCount = () => {
    const cartItems = document.querySelectorAll('app-cart-item-wrapper, app-shopping-cart-item');
    return cartItems.length;
  };

  const initialCartCount = getCurrentCartCount();
  console.log(`📊 Aktuelle Warenkorb-Items: ${initialCartCount}`);

  let attempts = 0;
  const maxAttempts = 20; // 10 Sekunden warten (500ms * 20)

  const checkCartEmpty = () => {
    attempts++;
    const currentCartCount = getCurrentCartCount();
    console.log(`🔍 Check ${attempts}/${maxAttempts}: ${currentCartCount} Items im Warenkorb`);

    // Prüfe ob Warenkorb leer ist
    if (currentCartCount === 0) {
      console.log('✅ Warenkorb ist leer! Erstelle neues Overlay...');
      recreateKioskOverlayWithFreshData();
      return;
    }

    // Prüfe ob sich die Anzahl verringert hat (auch wenn nicht komplett leer)
    if (currentCartCount < initialCartCount) {
      console.log(`📉 Warenkorb-Items reduziert von ${initialCartCount} auf ${currentCartCount}`);
      // Warte noch etwas länger für komplette Leerung
      setTimeout(() => {
        const finalCount = getCurrentCartCount();
        if (finalCount === 0) {
          console.log('✅ Warenkorb ist jetzt komplett leer!');
          recreateKioskOverlayWithFreshData();
        } else {
          console.log(`⚠️ Warenkorb nicht komplett leer (${finalCount} Items), erstelle trotzdem neues Overlay`);
          recreateKioskOverlayWithFreshData();
        }
      }, 1000);
      return;
    }

    if (attempts < maxAttempts) {
      setTimeout(checkCartEmpty, 500);
    } else {
      console.log('⏰ Timeout erreicht, erstelle trotzdem neues Overlay');
      recreateKioskOverlayWithFreshData();
    }
  };

  // Starte Überwachung nach kurzer Verzögerung
  setTimeout(checkCartEmpty, 500);
}

function recreateKioskOverlayWithFreshData() {
  console.log('🔄 Erstelle Kiosk-Overlay komplett neu mit frischen Daten...');
  
  // Entferne alle bestehenden Overlays
  const existingKioskOverlay = document.getElementById('kiosk-overlay');
  if (existingKioskOverlay) {
    existingKioskOverlay.remove();
    console.log('🗑️ Altes Kiosk-Overlay entfernt');
  }
  
  const existingCheckoutOverlay = document.getElementById('checkout-overlay');
  if (existingCheckoutOverlay) {
    existingCheckoutOverlay.remove();
    console.log('🗑️ Altes Checkout-Overlay entfernt');
  }
  
  // Entferne Checkout-Marker
  const checkoutMarker = document.getElementById('checkout-kiosk-applied');
  if (checkoutMarker) {
    checkoutMarker.remove();
    console.log('🗑️ Checkout-Marker entfernt');
  }
  
  // Original-Interface verstecken
  const appRoot = document.querySelector('app-root');
  if (appRoot) {
    appRoot.style.setProperty('display', 'none', 'important');
    console.log('👻 Original-Interface versteckt');
  }
  
  // Warte einen Moment für DOM-Stabilisierung, dann erstelle neues Overlay
  setTimeout(() => {
    console.log('🆕 Erstelle komplett neues Kiosk-Overlay...');
    createKioskOverlay();
    
    // Force-Reload der Daten nach weiteren 2 Sekunden
    setTimeout(() => {
      console.log('🔄 Force-Reload der Overlay-Daten...');
      loadOriginalData();
    }, 2000);
  }, 500);
}

// ============================
// INAKTIVITÄTS-TIMER-SYSTEM
// ============================

let inactivityTimer = null;
let countdownInterval = null;
let lastActivityTime = Date.now();
const INACTIVITY_TIMEOUT = 30000; // 30 Sekunden
const COUNTDOWN_DURATION = 10000; // 10 Sekunden

/**
 * Prüft ob der Warenkorb Items enthält
 */
function hasCartItems() {
  const cartItems = document.querySelectorAll('app-cart-item-wrapper, app-shopping-cart-item, .cart-item');
  return cartItems.length > 0;
}

/**
 * Erstellt das Countdown-Overlay
 */
function createInactivityCountdownOverlay() {
  console.log('⏰ Erstelle Inaktivitäts-Countdown-Overlay...');
  
  // Entferne existierendes Overlay
  const existing = document.getElementById('inactivity-countdown-overlay');
  if (existing) existing.remove();
  
  const overlay = document.createElement('div');
  overlay.id = 'inactivity-countdown-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 15000;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Roboto', sans-serif;
    animation: fadeIn 0.3s ease;
  `;
  
  overlay.innerHTML = `
    <div style="
      background-image: linear-gradient(135deg, #334155 0%, #273241FF 100%);
      color: white;
      padding: 3rem;
      border-radius: 20px;
      text-align: center;
      box-shadow: 0 2px 20px rgba(23, 29, 38, 1);
      max-width: 500px;
      margin: 2rem;
    ">
     
      <h2 style="
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #f59e0b;
      ">Sind Sie noch da?</h2>
      
      <p style="
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        line-height: 1.4;
      ">
        Der Vorgang wird automatisch abgebrochen in:
      </p>
      
      <div id="countdown-display" style="
        font-size: 4rem;
        font-weight: bold;
        color: #ef4444;
        margin-bottom: 2rem;
        text-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
      ">10</div>
      
      
      <button id="continue-session-button" style="
        background: #10b981;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 1rem 2.5rem;
        font-size: 2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
        letter-spacing: 1px;
      ">
        FORTFAHREN
      </button>
    </div>
  `;
  
  document.body.appendChild(overlay);
  
  // Countdown starten
  let timeLeft = 10;
  const countdownDisplay = document.getElementById('countdown-display');
  
  countdownInterval = setInterval(() => {
    timeLeft--;
    countdownDisplay.textContent = timeLeft;
    
    // Countdown-Farbe ändern bei weniger Zeit
    if (timeLeft <= 3) {
      countdownDisplay.style.color = '#dc2626';
      countdownDisplay.style.animation = 'pulse 0.5s ease-in-out infinite alternate';
    }
    
    if (timeLeft <= 0) {
      clearInterval(countdownInterval);
      handleInactivityTimeout();
    }
  }, 1000);
  
  // Event-Listener für Fortfahren-Button
  const continueButton = document.getElementById('continue-session-button');
  if (continueButton) {
    continueButton.addEventListener('click', cancelInactivityCountdown);
    
    // Hover-Effekt
    continueButton.addEventListener('mouseenter', () => {
      continueButton.style.transform = 'scale(1.05)';
      continueButton.style.background = '#0ea471';
    });
    
    continueButton.addEventListener('mouseleave', () => {
      continueButton.style.transform = 'scale(1)';
      continueButton.style.background = '#10b981';
    });
  }
  
  // Klick irgendwo zum Fortfahren
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      cancelInactivityCountdown();
    }
  });
}

/**
 * Bricht den Inaktivitäts-Countdown ab
 */
function cancelInactivityCountdown() {
  console.log('✅ Inaktivitäts-Countdown abgebrochen');
  
  if (countdownInterval) {
    clearInterval(countdownInterval);
    countdownInterval = null;
  }
  
  const overlay = document.getElementById('inactivity-countdown-overlay');
  if (overlay) {
    overlay.style.transition = 'opacity 0.3s ease';
    overlay.style.opacity = '0';
    setTimeout(() => overlay.remove(), 300);
  }
  
  // Timer neu starten
  resetInactivityTimer();
}

/**
 * Behandelt Timeout nach Inaktivität
 */
function handleInactivityTimeout() {
  console.log('⏰ Inaktivitäts-Timeout erreicht - lösche Warenkorb und gehe zum Startscreen');
  
  // Entferne Countdown-Overlay
  const overlay = document.getElementById('inactivity-countdown-overlay');
  if (overlay) overlay.remove();
  
  // Session zurücksetzen
  hasStartedSession = false;
  
  // Lösche Warenkorb
  triggerOriginalCartClear();
  
  // Warte kurz, dann zeige Startscreen
  setTimeout(() => {
    // Entferne alle Overlays
    const kioskOverlay = document.getElementById('kiosk-overlay');
    if (kioskOverlay) kioskOverlay.remove();
    
    const checkoutOverlay = document.getElementById('checkout-overlay');
    if (checkoutOverlay) checkoutOverlay.remove();
    
    // Zeige Startscreen
    createStartScreen();
    
    console.log('🏠 Zurück zum Startscreen nach Inaktivität');
  }, 1000);
}

/**
 * Startet den Inaktivitäts-Timer neu
 */
function resetInactivityTimer() {
  // Lösche bestehenden Timer
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }
  
  lastActivityTime = Date.now();
  
  // Starte Timer nur wenn Session aktiv ist und Warenkorb nicht leer
  if (hasStartedSession && hasCartItems()) {
    console.log('🔄 Inaktivitäts-Timer zurückgesetzt (30s)');
    
    inactivityTimer = setTimeout(() => {
      // Prüfe nochmals ob Warenkorb noch Items hat
      if (hasCartItems()) {
        console.log('⚠️ Inaktivität erkannt - zeige Countdown');
        createInactivityCountdownOverlay();
      }
    }, INACTIVITY_TIMEOUT);
  }
}

/**
 * Registriert Aktivität und setzt Timer zurück
 */
function registerActivity() {
  // Nur wenn Session aktiv ist
  if (hasStartedSession) {
    resetInactivityTimer();
  }
}

/**
 * Initialisiert das Inaktivitäts-Tracking
 */
function initializeInactivityTracking() {
  console.log('👀 Initialisiere Inaktivitäts-Tracking...');
  
  // Event-Listener für verschiedene Aktivitäten
  const activityEvents = [
    'click', 'touchstart', 'mousedown', 
    'mousemove', 'keypress', 'scroll'
  ];
  
  activityEvents.forEach(eventType => {
    document.addEventListener(eventType, registerActivity, { 
      passive: true, 
      capture: true 
    });
  });
  
  // CSS-Animation für Fade-In-Effekt (Pulse bereits in applyKioskStyles definiert)
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }
  `;
  document.head.appendChild(style);
  
  console.log('✅ Inaktivitäts-Tracking aktiviert');
}

// Inaktivitäts-Tracking beim DOM-Load starten
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeInactivityTracking);
} else {
  initializeInactivityTracking();
}

// =========================
// BUTTON MORPHING FUNCTIONS
// =========================

/**
 * Erstellt einen Ripple-Effekt an der Klickposition
 * @param {HTMLElement} button - Das Button-Element
 * @param {Event} event - Das Click-Event für die Position
 */
function createRippleEffect(button, event) {
  // Entferne existierende Ripples
  const existingRipples = button.querySelectorAll('.ripple-circle');
  existingRipples.forEach(ripple => ripple.remove());
  
  // Erstelle neuen Ripple
  const ripple = document.createElement('div');
  ripple.className = 'ripple-circle';
  
  // Berechne Position relativ zum Button
  const rect = button.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  
  // Style des Ripples
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    pointer-events: none;
    width: 20px;
    height: 20px;
    left: ${x - 10}px;
    top: ${y - 10}px;
    animation: button-ripple 0.6s ease-out;
    z-index: 1;
  `;
  
  button.appendChild(ripple);
  
  // Entferne Ripple nach Animation
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

/**
 * Fügt Press-Animation zu einem Button hinzu
 * @param {HTMLElement} button - Das Button-Element
 */
function addPressAnimation(button) {
  button.classList.add('button-pressed');
  
  setTimeout(() => {
    button.classList.remove('button-pressed');
  }, 200);
}

/**
 * Fügt Bounce-Animation für Erfolgsaktionen hinzu
 * @param {HTMLElement} button - Das Button-Element
 */
function addBounceAnimation(button) {
  button.classList.add('button-bounced');
  
  setTimeout(() => {
    button.classList.remove('button-bounced');
  }, 600);
}

/**
 * Hauptfunktion für Button-Morphing-Effekte
 * @param {HTMLElement} button - Das Button-Element
 * @param {Event} event - Das Click-Event
 * @param {string} type - Art des Effekts ('press', 'bounce', 'ripple')
 */
function triggerButtonMorphing(button, event, type = 'press') {
  if (!button) return;
  
  console.log(`🎭 Triggere ${type}-Effekt für Button:`, button.id || button.className);
  
  // Ripple-Effekt (für Touch-Feedback)
  if (type === 'ripple' || type === 'press') {
    createRippleEffect(button, event);
  }
  
  // Press-Animation (Standard)
  if (type === 'press') {
    addPressAnimation(button);
  }
  
  // Bounce-Animation (für Erfolgsaktionen)
  if (type === 'bounce') {
    addBounceAnimation(button);
  }
}

/**
 * Initialisiert Button-Morphing für alle relevanten Elemente
 */
function initializeButtonMorphing() {
  console.log('🎭 Initialisiere Button-Morphing-Effekte...');
  
  // Selector für alle morphbaren Buttons
  const buttonSelectors = [
    '.product-card',
    '#checkout-button',
    '#clear-cart-button', 
    '#start-session-button',
    '#checkout-pay-button',
    '#checkout-clear-cart-button',
    '#voucher-add-button'
  ];
  
  buttonSelectors.forEach(selector => {
    // Event-Delegation für dynamisch erstellte Elemente
    document.addEventListener('click', (event) => {
      const target = event.target;
      
      // Prüfe ob das geklickte Element oder ein Parent dem Selector entspricht
      const button = target.matches(selector) ? target : target.closest(selector);
      
      if (button) {
        // Bestimme Animationstyp basierend auf Button
        let animationType = 'press';
        
        if (button.id === 'checkout-button' || button.id === 'checkout-pay-button') {
          animationType = 'bounce'; // Erfolgsaktionen
        } else if (button.classList.contains('product-card')) {
          animationType = 'ripple'; // Produkt-Auswahl
        }
        
        triggerButtonMorphing(button, event, animationType);
      }
    });
  });
  
  console.log('✅ Button-Morphing-Event-Listener aktiviert');
}

// Button-Morphing beim DOM-Load initialisieren
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeButtonMorphing);
} else {
  initializeButtonMorphing();
}

console.log('Kiosk Preload-Skript geladen');

// =========================
// ENHANCED PULSE MONITORING
// =========================

/**
 * Überwacht den Checkout-Pay-Button und sorgt für sanfte Pulse-Animation
 */
function initializeEnhancedPulseMonitoring() {
  console.log('💓 Initialisiere verbesserte Pulse-Überwachung...');
  
  // MutationObserver für dynamische Checkout-Button-Erkennung
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // Überwache Checkout Pay Button
        const payButton = document.getElementById('checkout-pay-button');
        if (payButton && !payButton.classList.contains('pulse-enhanced')) {
          // Markiere als enhanced und stelle sicher, dass Animation läuft
          payButton.classList.add('pulse-enhanced');
          
          // Zusätzliche Glättung durch CSS-Klasse
          payButton.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          
          console.log('💓 Smooth Pulse für Pay-Button aktiviert');
        }
        
        // Überwache EC-Karten Button
        const ecCardButton = document.querySelector('body:has(app-checkout) app-payment-options .flex.justify-stretch button:has(.fa-credit-card)');
        if (ecCardButton && !ecCardButton.classList.contains('pulse-enhanced')) {
          // Markiere als enhanced und stelle sicher, dass Animation läuft
          ecCardButton.classList.add('pulse-enhanced');
          
          // Zusätzliche Glättung durch CSS-Klasse
          ecCardButton.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          
          console.log('💳 Smooth Pulse für EC-Karten-Button aktiviert');
        }
      }
    });
  });
  
  // Überwache Änderungen am gesamten Body
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Sofortige Initialisierung falls Buttons bereits vorhanden
  const existingPayButton = document.getElementById('checkout-pay-button');
  if (existingPayButton && !existingPayButton.classList.contains('pulse-enhanced')) {
    existingPayButton.classList.add('pulse-enhanced');
    existingPayButton.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    console.log('💓 Smooth Pulse für existierenden Pay-Button aktiviert');
  }
  
  const existingEcCardButton = document.querySelector('body:has(app-checkout) app-payment-options .flex.justify-stretch button:has(.fa-credit-card)');
  if (existingEcCardButton && !existingEcCardButton.classList.contains('pulse-enhanced')) {
    existingEcCardButton.classList.add('pulse-enhanced');
    existingEcCardButton.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    console.log('💳 Smooth Pulse für existierenden EC-Karten-Button aktiviert');
  }
}

// Starte Enhanced Pulse Monitoring beim DOM-Load
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeEnhancedPulseMonitoring);
} else {
  initializeEnhancedPulseMonitoring();
}