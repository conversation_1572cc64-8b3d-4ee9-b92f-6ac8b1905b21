const winston = require('winston');
const fs = require('fs');
const path = require('path');

// <PERSON><PERSON><PERSON>-Verzeichnis falls es nicht existiert
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// <PERSON>-Konfiguration
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack }) => {
      let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
      if (stack) {
        logMessage += `\n${stack}`;
      }
      return logMessage;
    })
  ),
  transports: [
    // Console-Output
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          let logMessage = `${timestamp} [${level}]: ${message}`;
          if (stack) {
            logMessage += `\n${stack}`;
          }
          return logMessage;
        })
      )
    }),
    
    // File-Output
    new winston.transports.File({
      filename: path.join(logsDir, 'kiosk.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 10,
      tailable: true
    }),
    
    // Error-File
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    })
  ],
  
  // Exception und Rejection Handling
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log')
    })
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log')
    })
  ]
});

// Zusätzliche Methoden für strukturiertes Logging
logger.api = (method, url, status, duration) => {
  logger.info(`API ${method} ${url} - Status: ${status} - Duration: ${duration}ms`);
};

logger.hardware = (device, action, status, details = '') => {
  logger.info(`Hardware [${device}] ${action} - Status: ${status} ${details}`);
};

logger.transaction = (transactionId, action, amount = null, details = '') => {
  const amountStr = amount ? ` - Amount: ${amount}€` : '';
  logger.info(`Transaction [${transactionId}] ${action}${amountStr} ${details}`);
};

logger.mqtt = (topic, action, payload = null) => {
  const payloadStr = payload ? ` - Payload: ${JSON.stringify(payload)}` : '';
  logger.info(`MQTT [${topic}] ${action}${payloadStr}`);
};

logger.system = (component, status, details = '') => {
  logger.info(`System [${component}] ${status} ${details}`);
};

// Startup-Log
logger.info('='.repeat(80));
logger.info('Wizid Kiosk Application gestartet');
logger.info(`Node.js Version: ${process.version}`);
logger.info(`Platform: ${process.platform} ${process.arch}`);
logger.info(`Working Directory: ${process.cwd()}`);
logger.info('='.repeat(80));

// Export des Loggers
module.exports = logger;