# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Build outputs
dist/
build/
out/

# Cache directories
.cache/
config/cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Electron specific
app/
release/

# Config files with sensitive data
config/local-config.json

# API config cache
config/cache/

# Hardware logs
hardware/logs/

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS specific  
.DS_Store
.AppleDouble
.LSOverride

# Linux specific
*~

# IDE specific
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp